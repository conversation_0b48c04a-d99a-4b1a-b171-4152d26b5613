<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip.corp.bff.service</groupId>
        <artifactId>template-parent-pom</artifactId>
        <version>1.0.28</version>
    </parent>

    <artifactId>basic-im-trip</artifactId>
    <packaging>war</packaging>
    <version>1.0.0</version>

    <properties>
        <java.version>21</java.version>
        <file_encoding>UTF-8</file_encoding>
        <jacoco.version>0.8.11</jacoco.version>
        <jmockit.version>1.52.0-jdk21</jmockit.version>
        <baiji.service.define>com.ctrip.corp.bff.im.trip.contract.CorpBffBasicImTripService</baiji.service.define>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ctrip.corp.bff.service</groupId>
                <artifactId>integration-entity</artifactId>
                <version>0.0.10</version>
            </dependency>
            <dependency>
                <artifactId>corpbfftools</artifactId>
                <groupId>com.ctrip.corp.31804</groupId>
                <version>0.0.14</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.corp.agg.base</groupId>
            <artifactId>basedataservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.bff.framework</groupId>
            <artifactId>corp-bff-framework-encrypt</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
            <version>1.0.3-java21</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.frontend.spock</groupId>
            <artifactId>spock-base-util</artifactId>
            <version>0.0.3</version>
        </dependency>
        <dependency>
            <groupId>qunar.tc.qschedule</groupId>
            <artifactId>qschedule-client</artifactId>
        </dependency>
        <!-- your service contract -->
        <dependency>
            <groupId>com.ctrip.corp.33295</groupId>
            <artifactId>corpbffbasicimtripservice</artifactId>
            <version>1.0.7-paymentlink-SNAPSHOT</version>
        </dependency>
        <!-- gateway -->
        <dependency>
            <groupId>com.ctrip.corp.bff.service</groupId>
            <artifactId>template-gateway</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>template-entity</artifactId>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tomcat-embed-core</artifactId>
                    <groupId>org.apache.tomcat.embed</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.idgen</groupId>
            <artifactId>idgen-client</artifactId>
        </dependency>
        <!-- Unit tests dependencies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <version>${jmockit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Unit tests dependencies -->
        <!--- 定义 jacoco 依赖  注意不是在dependencyManagement， dependencyManagement中只是声明-->
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <classifier>runtime</classifier>
        </dependency>
        <!-- IM集成服务 -->
        <dependency>
            <groupId>com.ctrip.corp.32071</groupId>
            <artifactId>corpbffbasicim</artifactId>
            <version>0.0.20</version>
        </dependency>
        <!--订单信息查询-->
        <dependency>
            <groupId>com.ctrip.soa.corp.order.orderindexservice.v1</groupId>
            <artifactId>orderindexservice</artifactId>
            <version>1.2.84</version>
        </dependency>
        <!--订单的工具类-->
        <dependency>
            <groupId>com.ctrip.corp.order</groupId>
            <artifactId>corporder-common</artifactId>
            <version>1.8.28</version>
            <exclusions>
                <exclusion>
                    <artifactId>corp-dal</artifactId>
                    <groupId>com.ctrip.corp.foundation</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.foundation</groupId>
                    <artifactId>corp-cloud-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.order</groupId>
            <artifactId>messagecenter-sender-contract</artifactId>
            <version>1.4.38.SmsSignature-SNAPSHOT</version>
        </dependency>
        <!--BFF基础工具服务校验敏感信息-->
        <dependency>
            <groupId>com.ctrip.corp.31804</groupId>
            <artifactId>corpbfftools</artifactId>
            <version>0.0.32</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>template-entity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                    <artifactId>integration-entity</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>basic-integration-entity</artifactId>
                    <groupId>com.ctrip.corp.bff.service</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <encoding>${file_encoding}</encoding>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <generatedSourcesDirectory>${project.build.directory}/generated-sources/</generatedSourcesDirectory>
                    <annotationProcessors>
                        <annotationProcessor>
                            com.ctrip.corp.bff.framework.template.service.generate.ServiceGenerateAnnotationProcessor
                        </annotationProcessor>
                    </annotationProcessors>

                    <showWarnings>true</showWarnings>
                    <fork>true</fork>
                    <compilerArgs>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>
                        <arg>-J--add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>
                        <arg>-ABaijiServiceDefine=${baiji.service.define}</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!--groovy 编译-->
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 注意不是在pluginManagement， pluginManagement中只是声明 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <!--离线模式必需指定， 否则到模块根目录而不是target目录了-->
                <configuration>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                    <argLine>
                        -javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar
                    </argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.4.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>