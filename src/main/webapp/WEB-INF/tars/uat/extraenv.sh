#!/bin/bash

# 获取 Java 版本
version=$(java -version 2>&1 | awk -F[\"_] 'NR==1{print $2}' | cut -d '.' -f 1)
FINAL_XMX_RATIO=65

# 如果提供了 CUSTOMER_XMX_RATIO，则使用它来覆盖默认值
if [[ -n "$CUSTOMER_XMX_RATIO" ]]; then
    FINAL_XMX_RATIO="$CUSTOMER_XMX_RATIO"
fi


# 检查自定义内存比例和 Java 版本
if [[ "$version" == "21" ]]; then
    # 确保 CDOS_MEM 已设置
    if [[ -z "$CDOS_MEM" ]]; then
        echo "Error: CDOS_MEM is not set."
        exit 1
    fi

    # 计算 JVM 内存大小
    JVM_XMX_MEM=$(echo "$CDOS_MEM * $FINAL_XMX_RATIO / 100" | bc -l)
    if [[ $? -ne 0 ]]; then
        echo "Error: Failed to calculate JVM memory size."
        exit 1
    fi
    JVM_XMX_MEM=${JVM_XMX_MEM%.*}

# 设置mirror环境开启对虚拟线程的跟踪
TRACE_PINNED_OPTS=""

if [[ "$PAAS_APP_IS_MIRROR_GROUP" = "true" ]]; then
TRACE_PINNED_OPTS="-Djdk.tracePinnedThreads=full"
fi

    # 设置 JVM 参数
    JAVA_OPTS="${JAVA_OPTS} \
    -XX:MaxDirectMemorySize=1024M \
    --add-opens java.base/java.nio=ALL-UNNAMED \
    -Dio.netty.tryReflectionSetAccessible=true \
    -Xms${JVM_XMX_MEM}m \
    -Xmx${JVM_XMX_MEM}m \
    -Dio.grpc.triplog.shaded.netty.shaded.io.netty.tryReflectionSetAccessible=true \
    -Dcdubbo.shade.io.netty.tryReflectionSetAccessible=true \
    ${TRACE_PINNED_OPTS}"
fi



