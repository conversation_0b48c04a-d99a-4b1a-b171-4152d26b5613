package com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.LetterListQueryRequestType;
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryRequestVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class MapperOfLetterListQueryRequestType extends AbstractMapper<Tuple1<LetterListQueryRequestVO>, LetterListQueryRequestType> {
    @Override
    protected LetterListQueryRequestType convert(Tuple1<LetterListQueryRequestVO> tuple1) {
        LetterListQueryRequestVO t1 = tuple1.getT1();
        LetterListQueryRequestType letterListQueryRequestType = new LetterListQueryRequestType();
        letterListQueryRequestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(t1.getRequestHeader()));
        letterListQueryRequestType.setPageIndex(t1.getPageIndex());
        letterListQueryRequestType.setPageSize(Objects.isNull(t1.getPageSize()) ? 10 : t1.getPageSize());
        return letterListQueryRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<LetterListQueryRequestVO> letterListQueryRequestVOTuple1) {
        return null;
    }
}
