package com.ctrip.corp.bff.im.trip.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
public enum ContentTypeEnum {
    /**
     * 普通消息
     */
    NORMAL_MESSAGE(1, "normalM  essage"),
    /**
     * offline消息
     */
    OFFLINE_MESSAGE(2, "offlineMessage");

    private Integer code;

    private String desc;


    ContentTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static Integer findCodeByDesc(String desc) {
        for (ContentTypeEnum item : ContentTypeEnum.values()) {
            if (StringUtil.equalsIgnoreCase(item.getDesc(), desc)) {
                return item.getCode();
            }
        }

        return null;
    }
}
