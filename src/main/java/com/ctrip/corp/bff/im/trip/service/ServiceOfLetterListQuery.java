package com.ctrip.corp.bff.im.trip.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.im.trip.common.enums.CommonErrorEnum;
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryRequestVO;
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryResponseVO;
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfLetterListQuery;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@WebService(name = "letterListQuery")
public class ServiceOfLetterListQuery extends AbstractSyncService<LetterListQueryRequestVO, LetterListQueryResponseVO> {

    @Autowired
    private ProcessorOfLetterListQuery processor;

    @Override
    public void validateRequest(LetterListQueryRequestVO requestType) throws BusinessException {
        if (Objects.isNull(requestType) || Objects.isNull(requestType.getRequestHeader()) || Objects.isNull(requestType.getPageIndex())) {
            throw BusinessExceptionBuilder.createAlertException(
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorMsg(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getSharkValue(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode() + "");
        }
    }

    @Override
    protected Processor<LetterListQueryRequestVO, LetterListQueryResponseVO> getProcessor(LetterListQueryRequestVO requestType) {
        return processor;
    }
}
