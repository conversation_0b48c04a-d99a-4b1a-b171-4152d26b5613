package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.im.contract.CorpBffBasicImServiceClient;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailCheckRequestType;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailCheckResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Component
public class HandlerOfVerificationCodeEmailCheck extends
    AbstractHandlerOfSOA<VerificationCodeEmailCheckRequestType, VerificationCodeEmailCheckResponseType, CorpBffBasicImServiceClient> {
    @Override
    protected String getMethodName() {
        return "verificationCodeEmailCheck";
    }
}
