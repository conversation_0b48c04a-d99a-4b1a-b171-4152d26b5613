package com.ctrip.corp.bff.im.trip.processor;

import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckRequestVO;
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckResponseVO;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient.HandlerOfVerificationCodeEmailCheck;
import com.ctrip.corp.bff.im.trip.mapper.mapperofverificationcodecheck.MapperOfVerificationCodeEmailCheckRequest;
import com.ctrip.corp.bff.im.trip.mapper.mapperofverificationcodecheck.MapperOfVerificationCodeEmailCheckResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Component
public class ProcessorOfVerificationCodeEmailCheck
    extends AbstractProcessor<VerificationCodeEmailCheckRequestVO, VerificationCodeEmailCheckResponseVO> {
    @Autowired
    private MapperOfVerificationCodeEmailCheckRequest mapperOfVerificationCodeEmailCheckRequest;
    @Autowired
    private HandlerOfVerificationCodeEmailCheck handlerOfVerificationCodeEmailCheck;
    @Autowired
    private MapperOfVerificationCodeEmailCheckResponseVO mapperOfVerificationCodeEmailCheckResponseVO;

    @Override
    public VerificationCodeEmailCheckResponseVO execute(VerificationCodeEmailCheckRequestVO request) throws Exception {
        // 获取验证结果
        return mapperOfVerificationCodeEmailCheckResponseVO.map(Tuple1.of(
            handlerOfVerificationCodeEmailCheck.handleAsync(
                mapperOfVerificationCodeEmailCheckRequest.map(Tuple1.of(request))).getWithoutError()));
    }

    @Override
    public Map<String, String> tracking(VerificationCodeEmailCheckRequestVO request,
        VerificationCodeEmailCheckResponseVO response) {
        return Collections.emptyMap();
    }
}
