package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.im.contract.CorpBffBasicImServiceClient;
import com.ctrip.corp.bff.im.contract.ImTripInitRequestType;
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/18
 * @contract <a href="http://contract.mobile.flight.ctripcorp.com/#/operation-detail/25103/16/imTripInit?lang=zh-CN">contract</a>
 */
@Component
public class HandlerOfImTripInit extends AbstractHandlerOfSOA<ImTripInitRequestType, ImTripInitResponseType, CorpBffBasicImServiceClient> {
    @Override
    protected String getMethodName() {
        return "imTripInit";
    }
}
