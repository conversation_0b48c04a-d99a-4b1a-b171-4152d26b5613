package com.ctrip.corp.bff.im.trip.mapper.mapperofcustomerinfoquery;

import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.vo.PhoneInfoVO;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.CustomerInfo;
import com.ctrip.corp.bff.im.contract.CustomerInfoQueryResponseType;
import com.ctrip.corp.bff.im.contract.VoipInfo;
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoQueryResponseVO;
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoVO;
import com.ctrip.corp.bff.im.trip.contract.VoipInfoVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: z.c. wang
 * @Description 联系信息查询vo出参
 * @Date: 2025/5/9 14:13
 * @Version 1.0
 */
@Component
public class MapperOfCustomerInfoQueryResponseVO extends AbstractMapper<Tuple1<CustomerInfoQueryResponseType>, CustomerInfoQueryResponseVO> {
    @Override
    protected CustomerInfoQueryResponseVO convert(Tuple1<CustomerInfoQueryResponseType> tuple) {
        CustomerInfoQueryResponseType customerInfoQueryResponseType = tuple.getT1();
        CustomerInfoQueryResponseVO customerInfoQueryResponseVO = new CustomerInfoQueryResponseVO();
        if (customerInfoQueryResponseType == null) {
            return customerInfoQueryResponseVO;
        }
        customerInfoQueryResponseVO.setCustomerInfos(buildCustomerInfos(customerInfoQueryResponseType.getCustomerInfos()));
        return customerInfoQueryResponseVO;
    }

    private List<CustomerInfoVO> buildCustomerInfos(List<CustomerInfo> customerInfos) {
        List<CustomerInfoVO> customerInfoVOS = new ArrayList<>();
        if (CollectionUtil.isEmpty(customerInfos)) {
            return customerInfoVOS;
        }
        for (CustomerInfo customerInfo : customerInfos) {
            if (customerInfo == null) {
                continue;
            }
            CustomerInfoVO customerInfoVO = new CustomerInfoVO();
            customerInfoVO.setCountry(customerInfo.getCountry());
            customerInfoVO.setPhoneInfo(buildPhoneInfoVO(customerInfo.getPhoneInfo()));
            customerInfoVO.setVoipInfo(buildVoipInfoVO(customerInfo.getVoipInfo()));
            customerInfoVOS.add(customerInfoVO);
        }
        return customerInfoVOS;
    }

    private PhoneInfoVO buildPhoneInfoVO(PhoneInfo phoneInfo) {
        if (phoneInfo == null) {
            return null;
        }
        PhoneInfoVO phoneInfoVO = new PhoneInfoVO();
        phoneInfoVO.setPhoneNo(phoneInfo.getPhoneNo());
        phoneInfoVO.setTransferPhoneNo(phoneInfo.getTransferPhoneNo());
        phoneInfoVO.setAreaCode(phoneInfo.getAreaCode());
        phoneInfoVO.setCountryCode(phoneInfo.getCountryCode());
        return phoneInfoVO;
    }

    private VoipInfoVO buildVoipInfoVO(VoipInfo voipInfo) {
        if (voipInfo == null) {
            return null;
        }
        VoipInfoVO voipInfoVO = new VoipInfoVO();
        voipInfoVO.setVoipDomain(voipInfo.getVoipDomain());
        voipInfoVO.setVoipNumber(voipInfo.getVoipNumber());
        voipInfoVO.setVoipPassword(voipInfo.getVoipPassword());
        voipInfoVO.setVoipUserName(voipInfo.getVoipUserName());
        return voipInfoVO;
    }

    @Override
    protected ParamCheckResult check(Tuple1<CustomerInfoQueryResponseType> tuple) {
        return null;
    }
}
