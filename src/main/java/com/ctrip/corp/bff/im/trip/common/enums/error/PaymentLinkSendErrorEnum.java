package com.ctrip.corp.bff.im.trip.common.enums.error;

import com.ctrip.corp.bff.framework.template.common.exception.business.IErrorInfo;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;

/**
 * <AUTHOR>
 * @date 2025/8/5
 */
public enum PaymentLinkSendErrorEnum implements IErrorInfo {

    /**
     * 请求参数错误
     */
    REQUEST_PARAM_CHECK_ERROR(600, "param error", "trip.biz.bff.paymentlinksend.error.param"),

    /**
     * manualSendMessage发消息失败
     */
    MANUAL_SEND_MESSAGE_ERROR(601, "manualSendMessage error", "trip.biz.bff.paymentlinksend.error.manualSendMessage"),

    /**
     * getOrderIndex获取产线失败，无法确定短信模板
     */
    PRODUCT_LINE_ERROR(602, "productLine error", "trip.biz.bff.paymentlinksend.error.productline"),

    /**
     * 缺失发送邮箱
     */
    MISS_EMAIL(603, "miss email", "trip.biz.bff.paymentlinksend.error.missemail"),

    /**
     * email数据错误
     */
    EMAIL_CHECK_DATA_ERROR(604, "email error", "trip.biz.bff.paymentlinksend.error.email"),
    ;

    private final int errorCode;

    private final String errorMsg;

    /**
     * 友好提示的shark key
     */
    private final String sharkKey;

    PaymentLinkSendErrorEnum(int errorCode, String errorMsg, String sharkKey) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.sharkKey = sharkKey;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    @Override public String getErrorMessage() {
        return errorMsg;
    }

    @Override public String getSharkKey() {
        return sharkKey;
    }

    @Override public String getFriendlyMessage() {
        return BFFSharkUtil.getSharkValue(sharkKey);
    }

}
