package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.ordermessagecentersenderserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageRequestType;
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageResponseType;
import com.ctrip.corp.order.messagecenter.sender.contract.OrderMessageCenterSenderServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/07/08
 */
@Component public class HandlerOfManualSendMessage
    extends AbstractHandlerOfSOA<ManualSendMessageRequestType, ManualSendMessageResponseType, OrderMessageCenterSenderServiceClient> {

    @Override protected String getMethodName() {
        return "manualSendMessage";
    }
}
