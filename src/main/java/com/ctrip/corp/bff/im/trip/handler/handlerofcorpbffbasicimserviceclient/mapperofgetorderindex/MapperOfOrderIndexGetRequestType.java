package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.mapperofgetorderindex;

import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.order.common.enumeration.request.OrderOperationChannelEnum;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/8 14:00
 */
@Component public class MapperOfOrderIndexGetRequestType
    extends AbstractMapper<Tuple2<TemplateSoaRequestType, String>, OrderIndexGetRequestType> {

    @Override protected OrderIndexGetRequestType convert(Tuple2<TemplateSoaRequestType, String> param) {
        TemplateSoaRequestType templateSoaRequestType = param.getT1();
        String orderId = param.getT2();
        OrderIndexGetRequestType orderIndexGetRequestType = new OrderIndexGetRequestType();
        orderIndexGetRequestType.setOrderId(TemplateNumberUtil.parseLong(orderId));
        orderIndexGetRequestType.setOperationChannel(buildOperationChannel(templateSoaRequestType));
        orderIndexGetRequestType.setDeleteFlag(true);
        return orderIndexGetRequestType;
    }

    @Override protected ParamCheckResult check(Tuple2<TemplateSoaRequestType, String> param) {
        return null;
    }

    protected String buildOperationChannel(TemplateSoaRequestType templateSoaRequestType) {
        return switch (templateSoaRequestType.getSourceFrom()) {
            case H5, Native, CRN -> OrderOperationChannelEnum.APP.getOperationChannel();
            case Offline -> OrderOperationChannelEnum.OFFLINE.getOperationChannel();
            default -> OrderOperationChannelEnum.ONLINE.getOperationChannel();
        };
    }
}
