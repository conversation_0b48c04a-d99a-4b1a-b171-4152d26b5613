package com.ctrip.corp.bff.im.trip.common.enums.error;

import com.ctrip.corp.bff.framework.template.common.exception.business.IErrorInfo;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
public enum ImTripInitErrorEnum implements IErrorInfo {

    // 参数校验异常400-499
    /**
     * 请求非法
     */
    INVALID_REQUEST(401, "request is invalid"),
    /**
     * orderId非法
     */
    INVALID_ORDER_ID(402, "orderId is invalid")
    ;

    private final Integer errorCode;

    private final String errorMessage;

    ImTripInitErrorEnum(Integer errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    @Override
    public Integer getErrorCode() {
        return errorCode;
    }

    @Override
    public String getErrorMessage() {
        return errorMessage;
    }
}
