package com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO;
import com.ctrip.corp.bff.tools.contract.CheckDataRequestType;
import com.ctrip.corp.bff.tools.contract.DataInfo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/23 20:37
 */
@Component public class MapperOfCheckDataRequestType
    extends AbstractMapper<Tuple1<PaymentLinkSendRequestVO>, CheckDataRequestType> {
    private static final String TYPE_EMAIL = "email";

    @Override protected CheckDataRequestType convert(Tuple1<PaymentLinkSendRequestVO> tuple) {
        PaymentLinkSendRequestVO paymentLinkSendRequestVO = tuple.getT1();
        CheckDataRequestType checkDataRequestType = new CheckDataRequestType();
        checkDataRequestType.setIntegrationSoaRequestType(
            SoaRequestUtil.convertVo2IntegrationRequest(paymentLinkSendRequestVO.getRequestHeader()));
        checkDataRequestType.setDataInfoList(getDataInfoList(paymentLinkSendRequestVO));
        return checkDataRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<PaymentLinkSendRequestVO> tuple) {
        return null;
    }

    private List<DataInfo> getDataInfoList(PaymentLinkSendRequestVO paymentLinkSendRequestVO) {
        List<DataInfo> dataInfos = new ArrayList<>();
        // 发送邮箱
        DataInfo dataInfo = new DataInfo();
        dataInfo.setData(paymentLinkSendRequestVO.getEmailInfo().getTransferEmail());
        dataInfo.setType(TYPE_EMAIL);
        dataInfo.setKey(paymentLinkSendRequestVO.getEmailInfo().getTransferEmail());
        dataInfos.add(dataInfo);
        return dataInfos;
    }
}
