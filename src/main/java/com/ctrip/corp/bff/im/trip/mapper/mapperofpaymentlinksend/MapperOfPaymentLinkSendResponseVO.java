package com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum;
import com.ctrip.corp.bff.im.trip.common.util.PaymentLinkSendUtil;
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendResponseVO;
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageResponseType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@Component public class MapperOfPaymentLinkSendResponseVO extends
    AbstractMapper<Tuple2<OrderIndexGetResponseType, ManualSendMessageResponseType>, PaymentLinkSendResponseVO> {
    public static final String DISABLE_SEND = "DISABLE_SEND";
    public static final String SUCCESS = "SUCCESS";
    public static final int SUCCESS_CODE = 20000;
    //当前订单状态不可继续支付，请检查后重试
    public static final String NOT_WAIT_PAY = "trip.biz.bff.paymentlinksend.error.notwaitpay";
    public static final String SEND_SUCCESS = "trip.biz.bff.paymentlinksend.success";

    @Override protected PaymentLinkSendResponseVO convert(
        Tuple2<OrderIndexGetResponseType, ManualSendMessageResponseType> tuple) {
        OrderIndexGetResponseType orderIndexGetResponseType = tuple.getT1();
        ManualSendMessageResponseType manualSendMessageResponseType = tuple.getT2();
        PaymentLinkSendResponseVO result = new PaymentLinkSendResponseVO();
        result.setSendResult(buildSendResult(manualSendMessageResponseType, orderIndexGetResponseType));
        result.setSendResultTip(buildSendResultTip(manualSendMessageResponseType, orderIndexGetResponseType));
        return result;
    }

    @Override protected ParamCheckResult check(Tuple2<OrderIndexGetResponseType, ManualSendMessageResponseType> tuple) {
        OrderIndexGetResponseType orderIndexGetResponseType = tuple.getT1();
        ManualSendMessageResponseType manualSendMessageResponseType = tuple.getT2();
        if (!PaymentLinkSendUtil.needManualSendMessage(orderIndexGetResponseType)) {
            return null;
        }
        if (SUCCESS_CODE != Optional.ofNullable(manualSendMessageResponseType)
            .map(ManualSendMessageResponseType::getResponseCode).orElse(0)) {
            throw BusinessExceptionBuilder.createAlertException(PaymentLinkSendErrorEnum.MANUAL_SEND_MESSAGE_ERROR,
                String.valueOf(PaymentLinkSendErrorEnum.MANUAL_SEND_MESSAGE_ERROR.getErrorCode()));
        }
        return null;
    }

    private String buildSendResult(ManualSendMessageResponseType manualSendMessageResponseType,
        OrderIndexGetResponseType orderIndexGetResponseType) {
        if (!PaymentLinkSendUtil.needManualSendMessage(orderIndexGetResponseType)) {
            return DISABLE_SEND;
        }
        if (SUCCESS_CODE == Optional.ofNullable(manualSendMessageResponseType)
            .map(ManualSendMessageResponseType::getResponseCode).orElse(0)) {
            return SUCCESS;
        }
        return null;
    }

    private String buildSendResultTip(ManualSendMessageResponseType manualSendMessageResponseType,
        OrderIndexGetResponseType orderIndexGetResponseType) {
        if (!PaymentLinkSendUtil.needManualSendMessage(orderIndexGetResponseType)) {
            return BFFSharkUtil.getSharkValue(NOT_WAIT_PAY);
        }
        if (SUCCESS_CODE == Optional.ofNullable(manualSendMessageResponseType)
            .map(ManualSendMessageResponseType::getResponseCode).orElse(0)) {
            return BFFSharkUtil.getSharkValue(SEND_SUCCESS);
        }
        return null;
    }
}
