package com.ctrip.corp.bff.im.trip.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
public enum LetterCategoryEnum {
    /**
     * 未知分类
     */
    UNKNOWN(0, "UNKNOWN"),
    /**
     * 审批/授权信息
     */
    AUTHORIZATION(1,"AUTHORIZATION"),
    /**
     * 行程管家
     */
    TRIP_MANAGEMENT(2, "TRIP_MANAGEMENT"),
    /**
     * 航变消息
     */
    FLIGHT_CHANGE(3, "FLIGHT_CHANGE"),
    /**
     * 订单通知
     */
    ORDER_NOTIFICATION(4, "ORDER_NOTIFICATION"),
    /**
     * 系统消息
     */
    SYSTEM_INFO(5, "SYSTEM_INFO"),
    /**
     * 活动通知
     */
    ACTIVITY_INFO(6, "ACTIVITY_INFO"),
    /**
     * 航班动态
     */
    FLIGHT_ACTIVITY(7, "FLIGHT_ACTIVITY"),
    /**
     * 企业信息
     */
    CORPORATION_INFO(8, "CORPORATION_INFO"),
    /**
     * 待办事项
     */
    PENDING_ITEMS(9, "PENDING_ITEMS"),
    /**
     * IM
     */
    IM_INFO(10, "IM_INFO");


    private Integer code;

    private String desc;

    LetterCategoryEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static LetterCategoryEnum findByCode(Integer code) {
        LetterCategoryEnum approvalTypeEnum = LetterCategoryEnum.UNKNOWN;
        for (LetterCategoryEnum item : LetterCategoryEnum.values()) {
            if (item.getCode().equals(code)) {
                approvalTypeEnum = item;
                break;
            }
        }

        return approvalTypeEnum;
    }

    public static Integer getCodeByDesc(String desc) {
        for (LetterCategoryEnum item : LetterCategoryEnum.values()) {
            if (StringUtil.equalsIgnoreCase(item.getDesc(), desc)) {
                return item.getCode();
            }
        }

        return null;
    }

}
