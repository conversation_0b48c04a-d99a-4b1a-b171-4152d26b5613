package com.ctrip.corp.bff.im.trip.mapper.mapofverificationcodeemailsend;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaCodeErrorMessageUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailSendResponseType;
import com.ctrip.corp.bff.im.ct.contract.EmailCodeSendResponseVO;
import com.ctrip.corp.bff.im.trip.common.constant.VerificationCodeSendConstant;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
@Component
public class MapperOfEmailCodeSendResponseVO
    extends AbstractMapper<Tuple1<VerificationCodeEmailSendResponseType>, EmailCodeSendResponseVO> {

    @Override
    protected EmailCodeSendResponseVO convert(Tuple1<VerificationCodeEmailSendResponseType> tuple) {
        EmailCodeSendResponseVO emailCodeSendResponseVO = new EmailCodeSendResponseVO();
        emailCodeSendResponseVO.setSendSuccess("T");
        return emailCodeSendResponseVO;
    }

    @Override
    protected ParamCheckResult check(Tuple1<VerificationCodeEmailSendResponseType> tuple) {
        VerificationCodeEmailSendResponseType response = tuple.getT1();
        if (response != null && "T".equalsIgnoreCase(response.getSendResult())) {
            return null;
        }
        String errorCode = Optional.ofNullable(response).map(VerificationCodeEmailSendResponseType::getIntegrationResponse)
                .map(IntegrationResponse::getErrorCode)
                .filter(StringUtil::isNotBlank).orElse("");
        String errorMessage = Optional.ofNullable(response).map(VerificationCodeEmailSendResponseType::getIntegrationResponse)
                .map(IntegrationResponse::getErrorMessage)
                .filter(StringUtil::isNotBlank).orElse("");
        SoaCodeErrorMessageUtil.SoaErrorOutEntity soaErrorOutEntity = SoaCodeErrorMessageUtil.getSoaErrorOut("CorpBffBasicImService",
                "emailCodeSend", errorCode, errorMessage);
        return new ParamCheckResult(Boolean.FALSE, Integer.parseInt(soaErrorOutEntity.getErrorCode()),
                "", errorMessage,
                BFFSharkUtil.getSharkValue(soaErrorOutEntity.getErrorMessage()));

    }
}
