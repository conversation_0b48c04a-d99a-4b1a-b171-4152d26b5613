package com.ctrip.corp.bff.im.trip.processor;

import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import com.ctrip.corp.bff.im.contract.LetterListQueryRequestType;
import com.ctrip.corp.bff.im.contract.LetterListQueryResponseType;
import com.ctrip.corp.bff.im.trip.common.constant.CountKeyConstant;
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryRequestVO;
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryResponseVO;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.HandlerOfCountInfoQuery;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.HandlerOfLetterListQuery;
import com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery.MapperOfCountInfoQueryRequest;
import com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery.MapperOfLetterListQueryRequestType;
import com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery.MapperOfLetterListQueryResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class ProcessorOfLetterListQuery extends AbstractProcessor<LetterListQueryRequestVO, LetterListQueryResponseVO> {

    @Autowired
    private MapperOfLetterListQueryResponseVO mapperOfLetterListQueryResponseVO;
    @Autowired
    private HandlerOfLetterListQuery handlerOfLetterListQuery;

    @Autowired
    private MapperOfLetterListQueryRequestType mapperOfLetterListQueryRequestType;

    @Autowired
    private MapperOfCountInfoQueryRequest mapperOfCountInfoQueryRequestType;

    @Autowired
    private HandlerOfCountInfoQuery handlerOfCountInfoQuery;

    @Override
    public LetterListQueryResponseVO execute(LetterListQueryRequestVO request) {
        WaitFuture<LetterListQueryRequestType, LetterListQueryResponseType> letterListQueryResponseWaitFuture =
                handlerOfLetterListQuery.handleAsync(mapperOfLetterListQueryRequestType.map(Tuple1.of(request)));

        WaitFuture<CountInfoQueryRequestType, CountInfoQueryResponseType> countInfoQueryResponseWaitFuture =
                handlerOfCountInfoQuery.handleAsync(mapperOfCountInfoQueryRequestType.map(Tuple2.of(request.getRequestHeader(), CountKeyConstant.UNREAD_MESSAGE_KEY)));
        return mapperOfLetterListQueryResponseVO.map(Tuple2.of(letterListQueryResponseWaitFuture.get(), countInfoQueryResponseWaitFuture.get()));
    }

    @Override
    public Map<String, String> tracking(LetterListQueryRequestVO request, LetterListQueryResponseVO response) {
        return null;
    }
}
