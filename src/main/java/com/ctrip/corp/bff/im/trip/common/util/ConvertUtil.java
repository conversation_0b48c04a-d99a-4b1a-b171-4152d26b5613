package com.ctrip.corp.bff.im.trip.common.util;

import com.ctrip.corp.bff.basic.contract.RiskControlInfo;
import com.ctrip.corp.bff.basic.contract.RiskControlInfoVO;
import com.ctrip.corp.bff.basic.contract.VerificationInfo;
import com.ctrip.corp.bff.basic.contract.VerificationInfoVO;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
public class ConvertUtil {

    public static EmailInfo buildEmailInfo(EmailInfoVO emailInfo) {
        if (emailInfo == null) {
            return null;
        }
        EmailInfo result = new EmailInfo();
        result.setTransferEmail(emailInfo.getTransferEmail());
        result.setEmail(emailInfo.getEmail());
        return result;
    }

    public static VerificationInfo buildVerificationInfo(VerificationInfoVO verificationInfo) {
        if (verificationInfo == null) {
            return null;
        }
        VerificationInfo result = new VerificationInfo();
        result.setCode(verificationInfo.getCode());
        result.setRid(verificationInfo.getRid());
        result.setVersion(verificationInfo.getVersion());
        result.setBusinessSite(verificationInfo.getBusinessSite());
        return result;
    }

    public static RiskControlInfo buildRiskControlInfo(RiskControlInfoVO riskControlInfo) {
        if (riskControlInfo == null) {
            return null;
        }
        RiskControlInfo result = new RiskControlInfo();
        result.setFingerPrint(riskControlInfo.getFingerPrint());
        result.setRmsToken(riskControlInfo.getRmsToken());
        return result;
    }
}
