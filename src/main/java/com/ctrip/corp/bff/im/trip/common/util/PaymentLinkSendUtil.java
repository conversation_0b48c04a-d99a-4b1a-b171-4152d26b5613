package com.ctrip.corp.bff.im.trip.common.util;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum;
import com.ctrip.corp.bff.tools.contract.CheckDataResponseType;
import com.ctrip.corp.bff.tools.contract.DataCheckResult;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderBasicInfoType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderDetailInfoType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/6
 */
public class PaymentLinkSendUtil {
    public static final String WAITPAY = "WaitPay";

    public static boolean needManualSendMessage(OrderIndexGetResponseType orderIndexGetResponseType) {
        String orderStatus =
            Optional.ofNullable(orderIndexGetResponseType).map(OrderIndexGetResponseType::getOrderDetailInfo)
                .map(OrderDetailInfoType::getBasicInfo).map(OrderBasicInfoType::getOrderStatus).orElse(null);
        if (!WAITPAY.equalsIgnoreCase(orderStatus)) {
            return false;
        }
        return true;
    }

    public static void checkData(CheckDataResponseType checkDataResponseType) {
        if (checkDataResponseType == null || CollectionUtil.isEmpty(checkDataResponseType.getResultList())) {
            return;
        }
        checkDataResponseType.getResultList().forEach(checkResult -> {
            if (checkResult == null) {
                return;
            }
            if (StringUtil.equalsIgnoreCase(checkResult.getCheckResult(), BooleanUtil.parseStr(false))) {
                throw BusinessExceptionBuilder.createAlertException(
                    PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR.getErrorCode(), checkResult.getFriendlyMessage(),
                    checkResult.getFriendlyMessage(),
                    String.valueOf(PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR.getErrorCode()));
            }
        });
    }
}
