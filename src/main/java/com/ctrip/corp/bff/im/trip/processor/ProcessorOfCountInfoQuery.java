package com.ctrip.corp.bff.im.trip.processor;

import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryRequestVO;
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryResponseVO;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.HandlerOfCountInfoQuery;
import com.ctrip.corp.bff.im.trip.mapper.mapperofcountinfoquery.MapperOfCountInfoQueryRequestType;
import com.ctrip.corp.bff.im.trip.mapper.mapperofcountinfoquery.MapperOfCountInfoQueryResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class ProcessorOfCountInfoQuery extends AbstractProcessor<CountInfoQueryRequestVO, CountInfoQueryResponseVO> {

    @Autowired
    private MapperOfCountInfoQueryRequestType mapperOfCountInfoQueryRequestType;

    @Autowired
    private HandlerOfCountInfoQuery handlerOfCountInfoQuery;

    @Autowired
    private MapperOfCountInfoQueryResponseVO mapperOfCountInfoQueryResponseVO;
    @Override
    public CountInfoQueryResponseVO execute(CountInfoQueryRequestVO request) {

        return mapperOfCountInfoQueryResponseVO.map(Tuple1.of(handlerOfCountInfoQuery.handleAsync(
                mapperOfCountInfoQueryRequestType.map(Tuple1.of(request))).get()));


    }

    @Override
    public Map<String, String> tracking(CountInfoQueryRequestVO request, CountInfoQueryResponseVO response) {

        return null;
    }
}
