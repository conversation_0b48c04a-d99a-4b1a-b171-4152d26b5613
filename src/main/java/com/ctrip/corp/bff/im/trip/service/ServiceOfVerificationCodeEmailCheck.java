package com.ctrip.corp.bff.im.trip.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.im.trip.common.enums.VerificationCodeCheckErrorEnum;
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckRequestVO;
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckResponseVO;
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfVerificationCodeEmailCheck;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/5/15
 */
@WebService(name = "verificationCodeEmailCheck", enableEncryptDecryptAop = true)
public class ServiceOfVerificationCodeEmailCheck
    extends AbstractSyncService<VerificationCodeEmailCheckRequestVO, VerificationCodeEmailCheckResponseVO> {
    @Autowired
    private ProcessorOfVerificationCodeEmailCheck processor;

    @Override
    public void validateRequest(VerificationCodeEmailCheckRequestVO request) throws BusinessException {
        if (StringUtil.isBlank(request.getVerificationCode())) {
            throw BusinessExceptionBuilder.createToastException(VerificationCodeCheckErrorEnum.VERIFICATION_CODE_EMPTY,
                null);
        }

    }

    @Override
    protected Processor<VerificationCodeEmailCheckRequestVO, VerificationCodeEmailCheckResponseVO> getProcessor(
        VerificationCodeEmailCheckRequestVO request) {
        return processor;
    }
}
