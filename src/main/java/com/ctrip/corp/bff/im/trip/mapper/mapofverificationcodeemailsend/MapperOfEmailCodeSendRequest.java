package com.ctrip.corp.bff.im.trip.mapper.mapofverificationcodeemailsend;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailSendRequestType;
import com.ctrip.corp.bff.im.ct.contract.EmailCodeSendRequestVO;
import com.ctrip.corp.bff.im.trip.common.util.ConvertUtil;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
@Component
public class MapperOfEmailCodeSendRequest
    extends AbstractMapper<Tuple1<EmailCodeSendRequestVO>, VerificationCodeEmailSendRequestType> {
    @Override
    protected VerificationCodeEmailSendRequestType convert(Tuple1<EmailCodeSendRequestVO> tuple) {
        EmailCodeSendRequestVO request = tuple.getT1();
        VerificationCodeEmailSendRequestType result = new VerificationCodeEmailSendRequestType();
        result.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(request.getRequestHeader()));
        result.setEmailInfo(ConvertUtil.buildEmailInfo(request.getEmailInfo()));
        result.setScenarioCode(request.getScenarioCode());
        result.setVerificationInfo(ConvertUtil.buildVerificationInfo(request.getVerificationInfo()));
        result.setRiskControlInfo(ConvertUtil.buildRiskControlInfo(request.getRiskControlInfo()));
        return result;
    }

    @Override
    protected ParamCheckResult check(Tuple1<EmailCodeSendRequestVO> tuple) {
        return null;
    }
}
