package com.ctrip.corp.bff.im.trip.processor;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.contract.ImTripInitRequestType;
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType;
import com.ctrip.corp.bff.im.trip.contract.ImTripInitRequestVO;
import com.ctrip.corp.bff.im.trip.contract.ImTripInitResponseVO;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient.HandlerOfImTripInit;
import com.ctrip.corp.bff.im.trip.mapper.mapperofimtripinit.MapperOfImTripInitRequest;
import com.ctrip.corp.bff.im.trip.mapper.mapperofimtripinit.MapperOfImTripInitResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * @Author: z.c. wang
 * @Date: 2024/11/13 16:45
 * @Version 1.0
 */
@Component
public class ProcessorOfImTripInit extends AbstractProcessor<ImTripInitRequestVO, ImTripInitResponseVO> {

    @Autowired
    private MapperOfImTripInitRequest mapperOfImTripInitRequest;
    @Autowired
    private HandlerOfImTripInit handlerOfImTripInit;
    @Autowired
    private MapperOfImTripInitResponseVO mapperOfImTripInitResponseVO;

    @Override
    public ImTripInitResponseVO execute(ImTripInitRequestVO request) throws Exception {
        boolean checkRequest = checkRequest(request);
        WaitFuture<ImTripInitRequestType, ImTripInitResponseType> imTripInitWaitFuture = null;
        if (checkRequest) {
            imTripInitWaitFuture = handlerOfImTripInit.handleAsync(
                    mapperOfImTripInitRequest.map(Tuple1.of(request)));
        }
        return mapperOfImTripInitResponseVO.map(Tuple1.of(
                Optional.ofNullable(imTripInitWaitFuture).map(WaitFuture::getWithoutError).orElse(null)));
    }

    private boolean checkRequest(ImTripInitRequestVO request) {
        if (request == null
                || request.getRequestHeader() == null
                || request.getRequestHeader().getHeader() == null
                || StringUtil.isBlank(request.getRequestHeader().getHeader().getUserId())
                || StringUtil.isBlank(request.getRequestHeader().getHeader().getCorpId())) {
            return false;
        }
        return true;
    }

    @Override
    public Map<String, String> tracking(ImTripInitRequestVO request, ImTripInitResponseVO response) {
        return null;
    }
}
