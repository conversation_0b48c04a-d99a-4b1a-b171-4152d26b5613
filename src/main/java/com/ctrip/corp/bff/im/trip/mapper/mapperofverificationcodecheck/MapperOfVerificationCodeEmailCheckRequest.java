package com.ctrip.corp.bff.im.trip.mapper.mapperofverificationcodecheck;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailCheckRequestType;
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckRequestVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Component
public class MapperOfVerificationCodeEmailCheckRequest
    extends AbstractMapper<Tuple1<VerificationCodeEmailCheckRequestVO>, VerificationCodeEmailCheckRequestType> {
    @Override
    protected VerificationCodeEmailCheckRequestType convert(Tuple1<VerificationCodeEmailCheckRequestVO> tuple1) {
        VerificationCodeEmailCheckRequestVO request = tuple1.getT1();
        VerificationCodeEmailCheckRequestType result = new VerificationCodeEmailCheckRequestType();
        result.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(request.getRequestHeader()));
        result.setVerificationCode(request.getVerificationCode());
        result.setEmailInfo(getEmailInfo(request.getEmailInfo()));
        result.setScenarioCode(request.getScenarioCode());
        return result;
    }

    private EmailInfo getEmailInfo(EmailInfoVO emailInfoVO) {
        EmailInfo emailInfo = new EmailInfo();
        emailInfo.setEmail(emailInfoVO.getEmail());
        emailInfo.setTransferEmail(emailInfoVO.getTransferEmail());
        return emailInfo;
    }

    @Override
    protected ParamCheckResult check(Tuple1<VerificationCodeEmailCheckRequestVO> tuple1) {
        return null;
    }
}
