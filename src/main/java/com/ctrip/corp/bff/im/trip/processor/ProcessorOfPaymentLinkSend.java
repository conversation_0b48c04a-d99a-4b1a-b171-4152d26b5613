package com.ctrip.corp.bff.im.trip.processor;

import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.trip.common.util.PaymentLinkSendUtil;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.corpbfftoolsservice.HandlerOfCheckData;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handleroforderindexserviceclient.HandlerOfGetOrderIndex;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.ordermessagecentersenderserviceclient.HandlerOfManualSendMessage;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.mapperofgetorderindex.MapperOfOrderIndexGetRequestType;
import com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend.MapperOfCheckDataRequestType;
import com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend.MapperOfManualManualSendMessageRequestType;
import com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend.MapperOfPaymentLinkSendResponseVO;
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO;
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendResponseVO;
import com.ctrip.corp.bff.tools.contract.CheckDataRequestType;
import com.ctrip.corp.bff.tools.contract.CheckDataResponseType;
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageRequestType;
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageResponseType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetRequestType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@Component public class ProcessorOfPaymentLinkSend
    extends AbstractProcessor<PaymentLinkSendRequestVO, PaymentLinkSendResponseVO> {
    @Autowired private HandlerOfGetOrderIndex handlerOfGetOrderIndex;
    @Autowired private MapperOfOrderIndexGetRequestType mapperOfOrderIndexGetRequestType;
    @Autowired private MapperOfPaymentLinkSendResponseVO mapperOfPaymentLinkSendResponseVO;
    @Autowired private HandlerOfManualSendMessage handlerOfManualSendMessage;
    @Autowired private MapperOfManualManualSendMessageRequestType mapperOfManualManualSendMessageRequestType;
    @Autowired private HandlerOfCheckData handlerOfCheckData;
    @Autowired private MapperOfCheckDataRequestType mapperOfCheckDataRequestType;

    @Override public PaymentLinkSendResponseVO execute(PaymentLinkSendRequestVO request) {
        // 敏感信息校验
        WaitFuture<CheckDataRequestType, CheckDataResponseType> checkDataWaitFuture =
            handlerOfCheckData.handleAsync(mapperOfCheckDataRequestType.map(Tuple1.of(request)));
        // email校验结果
        PaymentLinkSendUtil.checkData(checkDataWaitFuture.getWithoutError());
        WaitFuture<OrderIndexGetRequestType, OrderIndexGetResponseType> orderIndexGetRequestTypeWaitFuture =
            handlerOfGetOrderIndex.handleAsync(
                mapperOfOrderIndexGetRequestType.map(Tuple2.of(request.getRequestHeader(), request.getOrderId())));
        WaitFuture<ManualSendMessageRequestType, ManualSendMessageResponseType>
            manualSendMessageResponseTypeWaitFuture = null;
        if (PaymentLinkSendUtil.needManualSendMessage(orderIndexGetRequestTypeWaitFuture.getWithoutError())) {
            manualSendMessageResponseTypeWaitFuture = handlerOfManualSendMessage.handleAsync(
                mapperOfManualManualSendMessageRequestType.map(
                    Tuple2.of(orderIndexGetRequestTypeWaitFuture.getWithoutError(), request)));
        }
        return mapperOfPaymentLinkSendResponseVO.map(Tuple2.of(orderIndexGetRequestTypeWaitFuture.getWithoutError(),
            Optional.ofNullable(manualSendMessageResponseTypeWaitFuture).map(WaitFuture::getWithoutError)
                .orElse(null)));
    }

    @Override
    public Map<String, String> tracking(PaymentLinkSendRequestVO request, PaymentLinkSendResponseVO response) {

        return null;
    }
}
