package com.ctrip.corp.bff.im.trip.common.enums;

import com.ctrip.corp.bff.framework.template.common.exception.business.IErrorInfo;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;

/**
 * <AUTHOR>
 * @date 2024/4/23
 */
public enum VerificationCodeCheckErrorEnum implements IErrorInfo {

    /**
     * 验证码不能为空
     */
    VERIFICATION_CODE_EMPTY(400, "request params error", "trip.biz.text.verify.code.empty");
    /**
     * 错误码
     */
    private final Integer errorCode;
    /**
     * 描述
     */
    private final String errorTipMessage;
    /**
     * sharkKey
     */
    private final String errorTipSharkKey;

    VerificationCodeCheckErrorEnum(Integer errorCode, String errorTipMessage, String errorTipSharkKey) {
        this.errorCode = errorCode;
        this.errorTipMessage = errorTipMessage;
        this.errorTipSharkKey = errorTipSharkKey;
    }

    @Override
    public Integer getErrorCode() {
        return errorCode;
    }

    @Override
    public String getErrorMessage() {
        return errorTipMessage;
    }

    @Override
    public String getFriendlyMessage() {
        return BFFSharkUtil.getSharkValue(errorTipSharkKey);
    }
}
