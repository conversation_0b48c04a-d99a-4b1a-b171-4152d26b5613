package com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery;

import com.ctrip.corp.bff.framework.template.common.language.LanguageUtil;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import com.ctrip.corp.bff.im.contract.LetterListQueryResponseType;
import com.ctrip.corp.bff.im.trip.common.constant.CountKeyConstant;
import com.ctrip.corp.bff.im.trip.common.constant.NewSiteEnumConst;
import com.ctrip.corp.bff.im.trip.common.enums.ContentTypeEnum;
import com.ctrip.corp.bff.im.trip.common.enums.LetterCategoryEnum;
import com.ctrip.corp.bff.im.trip.common.enums.LetterImageCategoryEnum;
import com.ctrip.corp.bff.im.trip.contract.LetterInfoVO;
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryResponseVO;
import com.ctrip.corp.foundation.common.util.DateDisplayUtil;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class MapperOfLetterListQueryResponseVO extends AbstractMapper<Tuple2<LetterListQueryResponseType, CountInfoQueryResponseType>, LetterListQueryResponseVO> {

    @Override
    protected LetterListQueryResponseVO convert(Tuple2<LetterListQueryResponseType, CountInfoQueryResponseType> tuple) {
        LetterListQueryResponseVO letterListQueryResponseVO = new LetterListQueryResponseVO();
        Optional.ofNullable(tuple.getT2())
                .map(CountInfoQueryResponseType::getCountInfos)
                .filter(CollectionUtil::isNotEmpty)
                .flatMap(countInfos -> countInfos.stream()
                        .filter(countInfo -> StringUtil.equalsIgnoreCase(CountKeyConstant.UNREAD_MESSAGE_KEY, countInfo.getKey()))
                        .findFirst())
                .ifPresent(countInfo -> letterListQueryResponseVO.setUnreadCount(countInfo.getCount()));

        if (Objects.isNull(tuple.getT1())) {
            return letterListQueryResponseVO;
        }
        LetterListQueryResponseType t1 = tuple.getT1();

        letterListQueryResponseVO.setPageEnd(t1.getPageEnd());
        letterListQueryResponseVO.setTotalCount(t1.getTotalCount());

        if (CollectionUtil.isEmpty(t1.getLetterInfoList())) {
            return letterListQueryResponseVO;
        }

        List<LetterInfoVO> letterInfoList = t1.getLetterInfoList().stream().filter(Objects::nonNull).map(letter -> {
            LetterInfoVO letterInfoVO = new LetterInfoVO();
            letterInfoVO.setId(letter.getId());
            letterInfoVO.setContent(letter.getContent());
            letterInfoVO.setTitle(letter.getTitle());
            letterInfoVO.setUrl(letter.getMessageUrl());

            try {
                letterInfoVO.setCreateTime(DateDisplayUtil.ymdFullString(
                        DateUtil.fromStringToDate(letter.getCreateTime(), DateUtil.YYYY_MM_DD_HH_mm_ss), LanguageUtil.getLocale()));
            } catch (ParseException e) {
                LogUtil.loggingClogOnly(LogLevelEnum.Error, getClass(), "fromStringToDate", e, null);
            }
            letterInfoVO.setProductLine(getProductLine(letter.getSubProductLine()));
            letterInfoVO.setTrainSubLine(getSubTrainLine(letter.getSubProductLine()));
            letterInfoVO.setMessageType(LetterCategoryEnum.getCodeByDesc(letter.getMessageType()));
            letterInfoVO.setImageCategory(LetterImageCategoryEnum.getCodeByDesc(letter.getImageCategory()));
            letterInfoVO.setChangeNumber(letter.getChangeNumber());
            letterInfoVO.setOrderNumber(letter.getOrderId());
            letterInfoVO.setApprovalNumber(letter.getApprovalNumber());
            letterInfoVO.setContentType(ContentTypeEnum.findCodeByDesc(letter.getContentType()));
            letterInfoVO.setReadFlag(letter.getReadFlag());

            return letterInfoVO;
        }).collect(Collectors.toList());

        letterListQueryResponseVO.setLetterInfoList(letterInfoList);


        return letterListQueryResponseVO;


    }

    /**
     * 火车子产线
     *
     * @return
     */
    private String getSubTrainLine(String subProductLine) {
        if (StringUtil.isEmpty(subProductLine)) {
            return null;
        }
        return NewSiteEnumConst.TRAIN_SUB_LINE_MAP.get(subProductLine);
    }

    /**
     * 转换产线
     */
    public Integer getProductLine(String subProductLine) {
        if (StringUtil.isEmpty(subProductLine)) {
            return 0;
        }
        return Optional.ofNullable(NewSiteEnumConst.SITE_MAP.get(subProductLine)).orElse(0);
    }

    @Override
    protected ParamCheckResult check(Tuple2<LetterListQueryResponseType, CountInfoQueryResponseType> tuple) {
        return null;
    }
}
