package com.ctrip.corp.bff.im.trip.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum;
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfPaymentLinkSend;
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO;
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendResponseVO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@WebService(name = "paymentLinkSend") public class ServiceOfPaymentLinkSend
    extends AbstractSyncService<PaymentLinkSendRequestVO, PaymentLinkSendResponseVO> {

    @Autowired private ProcessorOfPaymentLinkSend processor;

    @Override public void validateRequest(PaymentLinkSendRequestVO request) throws BusinessException {
        if (StringUtil.isBlank(request.getOrderId())) {
            throw BusinessExceptionBuilder.createAlertException(PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR,
                String.valueOf(PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorCode()));
        }
        if (StringUtil.isBlank(Optional.ofNullable(request.getRequestHeader()).map(TemplateSoaRequestType::getHeader)
            .map(TemplateHeader::getUserId).orElse(null)) || StringUtil.isBlank(
            Optional.ofNullable(request.getRequestHeader()).map(TemplateSoaRequestType::getHeader)
                .map(TemplateHeader::getCorpId).orElse(null)) || StringUtil.isBlank(
            Optional.ofNullable(request.getRequestHeader()).map(TemplateSoaRequestType::getHeader)
                .map(TemplateHeader::getEid).orElse(null))) {
            throw BusinessExceptionBuilder.createAlertException(PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR,
                String.valueOf(PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorCode()));
        }
        if (StringUtil.isBlank(
            Optional.ofNullable(request.getEmailInfo()).map(EmailInfoVO::getTransferEmail).orElse(null))) {
            throw BusinessExceptionBuilder.createAlertException(PaymentLinkSendErrorEnum.MISS_EMAIL,
                String.valueOf(PaymentLinkSendErrorEnum.MISS_EMAIL.getErrorCode()));
        }
    }

    @Override protected Processor<PaymentLinkSendRequestVO, PaymentLinkSendResponseVO> getProcessor(
        PaymentLinkSendRequestVO paymentLinkSendRequestVO) {
        return processor;
    }
}
