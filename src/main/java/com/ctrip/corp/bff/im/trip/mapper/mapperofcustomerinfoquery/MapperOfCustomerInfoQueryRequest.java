package com.ctrip.corp.bff.im.trip.mapper.mapperofcustomerinfoquery;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.CustomerInfoQueryRequestType;
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoQueryRequestVO;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Description 联系信息集成服务入参
 * @Date: 2025/5/9 14:10
 * @Version 1.0
 */
@Component
public class MapperOfCustomerInfoQueryRequest extends AbstractMapper<Tuple1<CustomerInfoQueryRequestVO>, CustomerInfoQueryRequestType> {
    @Override
    protected CustomerInfoQueryRequestType convert(Tuple1<CustomerInfoQueryRequestVO> tuple) {
        CustomerInfoQueryRequestType customerInfoQueryRequestType = new CustomerInfoQueryRequestType();
        customerInfoQueryRequestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(tuple.getT1().getRequestHeader()));
        return customerInfoQueryRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<CustomerInfoQueryRequestVO> tuple) {
        return null;
    }
}
