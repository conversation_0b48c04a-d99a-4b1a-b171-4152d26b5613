package com.ctrip.corp.bff.im.trip.mapper.mapperofcountinfoquery;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType;
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryRequestVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class MapperOfCountInfoQueryRequestType extends AbstractMapper<Tuple1<CountInfoQueryRequestVO>, CountInfoQueryRequestType> {

    @Override
    protected CountInfoQueryRequestType convert(Tuple1<CountInfoQueryRequestVO> tuple) {
        CountInfoQueryRequestVO t1 = tuple.getT1();
        CountInfoQueryRequestType countInfoQueryRequestType = new CountInfoQueryRequestType();

        countInfoQueryRequestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(t1.getRequestHeader()));
        countInfoQueryRequestType.setQueryKeys(t1.getQueryKeys());


        return countInfoQueryRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<CountInfoQueryRequestVO> tuple) {
        return null;
    }
}
