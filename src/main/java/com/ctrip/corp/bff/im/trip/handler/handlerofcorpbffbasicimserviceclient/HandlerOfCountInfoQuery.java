package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import com.ctrip.corp.bff.im.contract.CorpBffBasicImServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class HandlerOfCountInfoQuery extends AbstractHandlerOfSOA<CountInfoQueryRequestType, CountInfoQueryResponseType, CorpBffBasicImServiceClient> {

    @Override
    protected String getMethodName() {
        return "countInfoQuery";
    }
}
