package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.im.contract.CorpBffBasicImServiceClient;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailSendRequestType;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailSendResponseType;
import org.springframework.stereotype.Component;

/**
 * @Description    
 * @author:  mmt
 * @Date: 2024/9/18
 */  
@Component
public class HandlerOfVerificationCodeEmailSend extends
    AbstractHandlerOfSOA<VerificationCodeEmailSendRequestType, VerificationCodeEmailSendResponseType, CorpBffBasicImServiceClient> {
    @Override
    protected String getMethodName() {
        return "verificationCodeEmailSend";
    }
}
