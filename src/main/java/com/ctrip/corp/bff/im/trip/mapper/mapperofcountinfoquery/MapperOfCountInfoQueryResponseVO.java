package com.ctrip.corp.bff.im.trip.mapper.mapperofcountinfoquery;

import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType;
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryResponseVO;
import com.ctrip.corp.bff.im.trip.contract.CountInfoVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class MapperOfCountInfoQueryResponseVO extends AbstractMapper<Tuple1<CountInfoQueryResponseType>, CountInfoQueryResponseVO> {

    @Override
    protected CountInfoQueryResponseVO convert(Tuple1<CountInfoQueryResponseType> tuple) {

        CountInfoQueryResponseType t1 = tuple.getT1();
        if (Objects.isNull(t1) || CollectionUtil.isEmpty(t1.getCountInfos())) {
            return null;
        }

        CountInfoQueryResponseVO countInfoQueryResponseVO = new CountInfoQueryResponseVO();
        List<CountInfoVO> collect = t1.getCountInfos().stream().filter(Objects::nonNull).map(i -> {
            CountInfoVO countInfoVO = new CountInfoVO();
            countInfoVO.setCount(i.getCount());
            countInfoVO.setKey(i.getKey());
            return countInfoVO;
        }).collect(Collectors.toList());
        countInfoQueryResponseVO.setCountInfos(collect);
        return countInfoQueryResponseVO;
    }

    @Override
    protected ParamCheckResult check(Tuple1<CountInfoQueryResponseType> tuple) {
        return null;
    }
}
