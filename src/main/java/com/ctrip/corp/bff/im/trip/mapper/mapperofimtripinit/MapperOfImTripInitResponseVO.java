package com.ctrip.corp.bff.im.trip.mapper.mapperofimtripinit;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.ImEntranceInfo;
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType;
import com.ctrip.corp.bff.im.contract.Question;
import com.ctrip.corp.bff.im.contract.QuestionInfo;
import com.ctrip.corp.bff.im.contract.ServiceContractInfo;
import com.ctrip.corp.bff.im.contract.ServiceEmailInfo;
import com.ctrip.corp.bff.im.contract.ServicePhoneInfo;
import com.ctrip.corp.bff.im.trip.contract.ImEntranceInfoVO;
import com.ctrip.corp.bff.im.trip.contract.ImTripInitResponseVO;
import com.ctrip.corp.bff.im.trip.contract.QuestionInfoVO;
import com.ctrip.corp.bff.im.trip.contract.QuestionVO;
import com.ctrip.corp.bff.im.trip.contract.ServiceContractInfoVO;
import com.ctrip.corp.bff.im.trip.contract.ServiceEmailInfoVO;
import com.ctrip.corp.bff.im.trip.contract.ServicePhoneInfoVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: z.c. wang
 * @Date: 2024/11/13 21:27
 * @Version 1.0
 */
@Component
public class MapperOfImTripInitResponseVO extends AbstractMapper<Tuple1<ImTripInitResponseType>, ImTripInitResponseVO> {

    @Override
    protected ImTripInitResponseVO convert(Tuple1<ImTripInitResponseType> tuple) {
        ImTripInitResponseType imTripInitResponse = tuple.getT1();
        ImTripInitResponseVO imTripInitResponseVO = new ImTripInitResponseVO();
        if (imTripInitResponse == null) {
            return imTripInitResponseVO;
        }
        imTripInitResponseVO.setImEntranceInfoList(buildImEntranceInfoList(imTripInitResponse.getImEntranceInfoList()));
        imTripInitResponseVO.setServiceContractInfo(buildServiceContractInfo(imTripInitResponse.getServiceContractInfo()));
        imTripInitResponseVO.setQuestionInfoList(buildQuestionInfoVO(imTripInitResponse.getQuestionInfoList()));
        imTripInitResponseVO.setSceneType(imTripInitResponse.getSceneType());
        return imTripInitResponseVO;
    }

    private List<QuestionInfoVO> buildQuestionInfoVO(List<QuestionInfo> questionInfoList) {
        if (CollectionUtil.isEmpty(questionInfoList)) {
            return null;
        }
        List<QuestionInfoVO> questionInfoVOList = new ArrayList<>();
        for (QuestionInfo questionInfo : questionInfoList) {
            List<Question> questionList = questionInfo.getQuestionList();
            if (CollectionUtil.isEmpty(questionList)) {
                continue;
            }
            QuestionInfoVO questionInfoVO = new QuestionInfoVO();
            questionInfoVO.setType(questionInfo.getType());
            questionInfoVO.setQuestionList(questionList.stream().map(question -> {
                QuestionVO questionVO = new QuestionVO();
                questionVO.setKey(question.getKey());
                questionVO.setQuestion(question.getQuestion());
                return questionVO;
            }).collect(Collectors.toList()));
            questionInfoVOList.add(questionInfoVO);
        }

        return questionInfoVOList;
    }

    private ServiceContractInfoVO buildServiceContractInfo(ServiceContractInfo serviceContractInfo) {
        if (serviceContractInfo == null) {
            return null;
        }
        ServiceContractInfoVO serviceContractInfoVO = new ServiceContractInfoVO();
        List<ServiceEmailInfo> serviceEmailInfoList = serviceContractInfo.getServiceEmailInfoList();
        if (CollectionUtil.isNotEmpty(serviceEmailInfoList)) {
            serviceContractInfoVO.setServiceEmailInfoList(serviceEmailInfoList.stream().map(serviceEmailInfo -> {
                ServiceEmailInfoVO serviceEmailInfoVO = new ServiceEmailInfoVO();
                serviceEmailInfoVO.setEmail(serviceEmailInfo.getEmail());
                return serviceEmailInfoVO;
            }).collect(Collectors.toList()));
        }
        List<ServicePhoneInfo> servicePhoneInfoList = serviceContractInfo.getServicePhoneInfoList();
        if (CollectionUtil.isNotEmpty(servicePhoneInfoList)) {
            serviceContractInfoVO.setServicePhoneInfoList(servicePhoneInfoList.stream().map(servicePhoneInfo -> {
                ServicePhoneInfoVO servicePhoneInfoVO = new ServicePhoneInfoVO();
                servicePhoneInfoVO.setPhone(servicePhoneInfo.getPhone());
                servicePhoneInfoVO.setCountryCode(servicePhoneInfo.getCountryCode());
                servicePhoneInfoVO.setCountryName(servicePhoneInfo.getCountryName());
                return servicePhoneInfoVO;
            }).collect(Collectors.toList()));
        }
        return serviceContractInfoVO;
    }

    private List<ImEntranceInfoVO> buildImEntranceInfoList(List<ImEntranceInfo> imEntranceInfoList) {
        if (CollectionUtil.isEmpty(imEntranceInfoList)) {
            return null;
        }
        return imEntranceInfoList.stream().map(imEntranceInfo -> {
            ImEntranceInfoVO imEntranceInfoVO = new ImEntranceInfoVO();
            imEntranceInfoVO.setType(imEntranceInfo.getType());
            imEntranceInfoVO.setName(BFFSharkUtil.getSharkValue("trip.biz.bff.common.im.trip.entrance." + imEntranceInfo.getType().toLowerCase()));
            imEntranceInfoVO.setIcon(imEntranceInfo.getIcon());
            imEntranceInfoVO.setBizLine(imEntranceInfo.getBizLine());
            return imEntranceInfoVO;
        }).collect(Collectors.toList());
    }

    @Override
    protected ParamCheckResult check(Tuple1<ImTripInitResponseType> tuple) {
        return null;
    }
}
