package com.ctrip.corp.bff.im.trip.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.im.trip.common.enums.CommonErrorEnum;
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryRequestVO;
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryResponseVO;
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfCountInfoQuery;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@WebService(name = "countInfoQuery")
public class ServiceOfCountInfoQuery extends AbstractSyncService<CountInfoQueryRequestVO, CountInfoQueryResponseVO> {

    @Autowired
    private ProcessorOfCountInfoQuery processor;

    @Override
    public void validateRequest(CountInfoQueryRequestVO requestType) throws BusinessException {
        if (Objects.isNull(requestType) || Objects.isNull(requestType.getRequestHeader()) || CollectionUtil.isEmpty(requestType.getQueryKeys())) {
            throw BusinessExceptionBuilder.createAlertException(
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorMsg(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getSharkValue(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode() + "");
        }


    }

    @Override
    protected Processor<CountInfoQueryRequestVO, CountInfoQueryResponseVO> getProcessor(CountInfoQueryRequestVO requestType) {
        return processor;
    }
}
