package com.ctrip.corp.bff.im.trip.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
public enum LetterImageCategoryEnum {
    /**
     * 机票订单相关
     */
    FLIGHT_ORDER(1, "FLIGHT_ORDER"),
    /**
     * 酒店订单相关
     */
    HOTEL_ORDER(2, "HOTEL_ORDER"),
    /**
     * 火车票订单相关
     */
    TRAIN_ORDER(3, "TRAIN_ORDER"),
    /**
     * 审批、账户、系统、企业通知
     */
    APPROVAL_ACCOUNT(4, "APPROVAL_ACCOUNT"),
    /**
     * 活动通知
     */
    ACTIVITY(5, "ACTIVITY"),
    /**
     * 其他
     */
    OTHER(6, "OTHER"),
    /**
     * 用车相关
     */
    CAR_ORDER(7, "CAR_ORDER");

    private Integer code;
    private String desc;

    LetterImageCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static Integer getCodeByDesc(String desc) {
        for (LetterImageCategoryEnum item : LetterImageCategoryEnum.values()) {
            if (StringUtil.equalsIgnoreCase(item.getDesc(), desc)) {
                return item.getCode();
            }
        }

        return null;
    }
}
