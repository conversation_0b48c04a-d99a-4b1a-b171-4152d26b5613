package com.ctrip.corp.bff.im.trip.processor;

import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.contract.CustomerInfoQueryRequestType;
import com.ctrip.corp.bff.im.contract.CustomerInfoQueryResponseType;
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoQueryRequestVO;
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoQueryResponseVO;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient.HandlerOfCustomerInfoQuery;
import com.ctrip.corp.bff.im.trip.mapper.mapperofcustomerinfoquery.MapperOfCustomerInfoQueryRequest;
import com.ctrip.corp.bff.im.trip.mapper.mapperofcustomerinfoquery.MapperOfCustomerInfoQueryResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: z.c. wang
 * @Description 联系信息查询
 * @Date: 2025/5/9 14:07
 * @Version 1.0
 */
@Component
public class ProcessorOfCustomerInfoQuery
        extends AbstractProcessor<CustomerInfoQueryRequestVO, CustomerInfoQueryResponseVO> {

    @Autowired
    private MapperOfCustomerInfoQueryRequest mapperOfCustomerInfoQueryRequest;
    @Autowired
    private HandlerOfCustomerInfoQuery handlerOfCustomerInfoQuery;
    @Autowired
    private MapperOfCustomerInfoQueryResponseVO mapperOfCustomerInfoQueryResponseVO;

    @Override
    public CustomerInfoQueryResponseVO execute(CustomerInfoQueryRequestVO request) throws Exception {
        WaitFuture<CustomerInfoQueryRequestType, CustomerInfoQueryResponseType> customerInfoQueryWaitFuture =
                handlerOfCustomerInfoQuery.handleAsync(mapperOfCustomerInfoQueryRequest.map(Tuple1.of(request)));
        return mapperOfCustomerInfoQueryResponseVO.map(Tuple1.of(customerInfoQueryWaitFuture.getWithoutError()));
    }

    @Override
    public Map<String, String> tracking(CustomerInfoQueryRequestVO request, CustomerInfoQueryResponseVO response) {
        return null;
    }
}
