package com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class MapperOfCountInfoQueryRequest extends AbstractMapper<Tuple2<TemplateSoaRequestType, String>, CountInfoQueryRequestType> {

    @Override
    protected CountInfoQueryRequestType convert(Tuple2<TemplateSoaRequestType, String> tuple) {
        CountInfoQueryRequestType countInfoQueryRequestType = new CountInfoQueryRequestType();

        countInfoQueryRequestType.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(tuple.getT1()));
        countInfoQueryRequestType.setQueryKeys(Collections.singletonList(tuple.getT2()));

        return countInfoQueryRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<TemplateSoaRequestType, String> tuple) {
        return null;
    }
}
