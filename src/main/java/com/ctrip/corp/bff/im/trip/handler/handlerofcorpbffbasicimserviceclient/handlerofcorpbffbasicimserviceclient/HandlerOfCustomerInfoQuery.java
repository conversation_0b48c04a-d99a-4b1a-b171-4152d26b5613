package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.im.contract.*;
import org.springframework.stereotype.Component;

/**
 * @Author: z.c. wang
 * @Description 联系信息查询
 * @Date: 2025/5/9 14:09
 * @Version 1.0
 */
@Component
public class HandlerOfCustomerInfoQuery extends
        AbstractHandlerOfSOA<CustomerInfoQueryRequestType, CustomerInfoQueryResponseType, CorpBffBasicImServiceClient> {
    @Override
    protected String getMethodName() {
        return "customerInfoQuery";
    }
}
