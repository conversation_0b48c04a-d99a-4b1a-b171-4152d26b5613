package com.ctrip.corp.bff.im.trip.processor;

import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailSendRequestType;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailSendResponseType;
import com.ctrip.corp.bff.im.ct.contract.EmailCodeSendRequestVO;
import com.ctrip.corp.bff.im.ct.contract.EmailCodeSendResponseVO;
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient.HandlerOfVerificationCodeEmailSend;
import com.ctrip.corp.bff.im.trip.mapper.mapofverificationcodeemailsend.MapperOfEmailCodeSendRequest;
import com.ctrip.corp.bff.im.trip.mapper.mapofverificationcodeemailsend.MapperOfEmailCodeSendResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
@Component
public class ProcessorOfEmailCodeSend
    extends AbstractProcessor<EmailCodeSendRequestVO, EmailCodeSendResponseVO> {
    @Autowired
    private HandlerOfVerificationCodeEmailSend handlerOfVerificationCodeEmailSend;
    @Autowired
    private MapperOfEmailCodeSendRequest mapperOfEmailCodeSendRequest;
    @Autowired
    private MapperOfEmailCodeSendResponseVO mapperOfEmailCodeSendResponseVO;

    @Override
    public EmailCodeSendResponseVO execute(EmailCodeSendRequestVO request) {
        VerificationCodeEmailSendRequestType verificationCodeEmailSendRequest =
                mapperOfEmailCodeSendRequest.map(Tuple1.of(request));
        VerificationCodeEmailSendResponseType verificationCodeEmailSendResponse =
            handlerOfVerificationCodeEmailSend.handleAsync(verificationCodeEmailSendRequest).getWithoutError();
        return mapperOfEmailCodeSendResponseVO.map(Tuple1.of(verificationCodeEmailSendResponse));
    }

    @Override
    public Map<String, String> tracking(EmailCodeSendRequestVO request,
                                        EmailCodeSendResponseVO response) {
        return Collections.emptyMap();
    }
}
