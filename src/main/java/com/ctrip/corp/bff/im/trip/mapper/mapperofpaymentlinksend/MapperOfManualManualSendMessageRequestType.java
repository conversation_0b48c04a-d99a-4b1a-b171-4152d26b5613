package com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum;
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO;
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageRequestType;
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageResponseType;
import com.ctrip.corp.order.messagecenter.sender.contract.PairType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderBasicInfoType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderDetailInfoType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@Component public class MapperOfManualManualSendMessageRequestType
    extends AbstractMapper<Tuple2<OrderIndexGetResponseType, PaymentLinkSendRequestVO>, ManualSendMessageRequestType> {
    private static final String BOOKING_USER = "BOOKING_USER";
    private static final String OTHER_USER = "OTHER_USER";
    private static final String TARGET = "target";
    private static final String EMAIL = "EMAIL";
    private static final String MESSAGE_TYPE = "messageType";
    private static final String MESSAGE_LANGUAGE = "language";
    private static final String LIEU_PAYMENT_NOTIFY_MANUAL = "Lieu_Payment_Notify_Manual";
    private static final String PAYMENT_NOTIFY_MANUAL = "Payment_Notify_Manual";
    private static final String REBOOK_PAYMENT_NOTIFY_MANUAL = "Rebook_Payment_Notify_Manual";
    private static final String REBOOK_LIEU_PAYMENT_NOTIFY_MANUAL = "Rebook_Lieu_Payment_Notify_Manual";

    @Override
    protected ManualSendMessageRequestType convert(Tuple2<OrderIndexGetResponseType, PaymentLinkSendRequestVO> tuple) {
        PaymentLinkSendRequestVO paymentLinkSendRequestVO = tuple.getT2();
        TemplateSoaRequestType templateSoaRequestType = paymentLinkSendRequestVO.getRequestHeader();
        OrderIndexGetResponseType orderIndexGetResponseType = tuple.getT1();
        ManualSendMessageRequestType result = new ManualSendMessageRequestType();
        result.setRequestId(templateSoaRequestType.getRequestId());
        result.setEid(templateSoaRequestType.getHeader().getEid());
        result.setCorpID(templateSoaRequestType.getHeader().getCorpId());
        result.setLanguage(templateSoaRequestType.getLanguage());
        result.setOrderID(TemplateNumberUtil.parseLong(paymentLinkSendRequestVO.getOrderId()));
        result.setUserID(templateSoaRequestType.getHeader().getUserId());
        result.setScenarioCode(buildScenarioCode(orderIndexGetResponseType, paymentLinkSendRequestVO));
        List<PairType> pairList = new ArrayList<>();
        pairList.add(new PairType(MESSAGE_TYPE, EMAIL));
        pairList.add(new PairType(TARGET, paymentLinkSendRequestVO.getEmailInfo().getTransferEmail()));
        pairList.add(new PairType(MESSAGE_LANGUAGE, paymentLinkSendRequestVO.getSendEmailLanguage()));
        result.setPairList(pairList);
        result.setProductLine(
            Optional.ofNullable(orderIndexGetResponseType).map(OrderIndexGetResponseType::getOrderDetailInfo)
                .map(OrderDetailInfoType::getBasicInfo).map(OrderBasicInfoType::getProductLine).orElse(null));
        return result;
    }

    @Override protected ParamCheckResult check(Tuple2<OrderIndexGetResponseType, PaymentLinkSendRequestVO> tuple) {
        OrderIndexGetResponseType orderIndexGetResponseType = tuple.getT1();
        if (TemplateNumberUtil.isNotZeroAndNull(
            Optional.ofNullable(orderIndexGetResponseType).map(OrderIndexGetResponseType::getOrderDetailInfo)
                .map(OrderDetailInfoType::getBasicInfo).map(OrderBasicInfoType::getProductLine).orElse(null))) {
            throw BusinessExceptionBuilder.createAlertException(PaymentLinkSendErrorEnum.PRODUCT_LINE_ERROR,
                String.valueOf(PaymentLinkSendErrorEnum.PRODUCT_LINE_ERROR.getErrorCode()));
        }
        return null;
    }

    /**
     * 全产线代付场景消息：Lieu_Payment_Notify_Manual
     * 用车、酒店、火车自付场景：Payment_Notify_Manual
     * 机票火车改签代付场景：Rebook_Lieu_Payment_Notify_Manual
     * 机票火车改签自付场景：Rebook_Payment_Notify_Manual
     * todo:改签是否有必要，发消息的内容 有orderid接口应该都能知道吧？
     *
     * @param orderIndexGetResponseType
     * @return
     */
    protected String buildScenarioCode(OrderIndexGetResponseType orderIndexGetResponseType,
        PaymentLinkSendRequestVO request) {
        if (BOOKING_USER.equalsIgnoreCase(request.getPaymentMethodCode())) {
            return PAYMENT_NOTIFY_MANUAL;
        }
        if (OTHER_USER.equalsIgnoreCase(request.getPaymentMethodCode())) {
            return LIEU_PAYMENT_NOTIFY_MANUAL;
        }
        return null;
    }
}
