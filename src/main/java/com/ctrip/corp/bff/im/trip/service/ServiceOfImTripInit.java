package com.ctrip.corp.bff.im.trip.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.im.trip.common.enums.error.ImTripInitErrorEnum;
import com.ctrip.corp.bff.im.trip.contract.ImTripInitRequestVO;
import com.ctrip.corp.bff.im.trip.contract.ImTripInitResponseVO;
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfImTripInit;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * im入口信息
 * @Author: z.c. wang
 * @Date: 2024/11/13 16:42
 * @Version 1.0
 */
@WebService(name = "imTripInit")
public class ServiceOfImTripInit extends
        AbstractSyncService<ImTripInitRequestVO, ImTripInitResponseVO> {

    @Autowired
    private ProcessorOfImTripInit processorOfImTripInit;

    @Override
    public void validateRequest(ImTripInitRequestVO imTripInitRequestVO) throws BusinessException {
        if (imTripInitRequestVO == null
                || imTripInitRequestVO.getRequestHeader() == null
                || imTripInitRequestVO.getRequestHeader().getHeader() == null) {
            throw BusinessExceptionBuilder.createToastException(ImTripInitErrorEnum.INVALID_REQUEST, null);
        }
        String orderId = imTripInitRequestVO.getOrderId();
        if (StringUtil.isNotBlank(orderId) && !StringUtil.isNumber(orderId)) {
            throw BusinessExceptionBuilder.createToastException(ImTripInitErrorEnum.INVALID_ORDER_ID, null);
        }
    }

    @Override
    protected Processor<ImTripInitRequestVO, ImTripInitResponseVO> getProcessor(ImTripInitRequestVO imTripInitRequestVO) {
        return processorOfImTripInit;
    }
}
