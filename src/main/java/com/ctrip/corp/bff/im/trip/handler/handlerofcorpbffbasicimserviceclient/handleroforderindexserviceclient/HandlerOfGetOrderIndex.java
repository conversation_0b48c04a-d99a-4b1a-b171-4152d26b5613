package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handleroforderindexserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetRequestType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType;
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/07/08
 */
@Component public class HandlerOfGetOrderIndex
    extends AbstractHandlerOfSOA<OrderIndexGetRequestType, OrderIndexGetResponseType, OrderIndexServiceClient> {

    @Override protected String getMethodName() {
        return "getOrderIndex";
    }
}
