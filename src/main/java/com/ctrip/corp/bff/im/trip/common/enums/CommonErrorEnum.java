package com.ctrip.corp.bff.im.trip.common.enums;

import com.ctrip.corp.bff.framework.template.common.shark.CommonEnumShark;

/**
 * <AUTHOR>
 * @date 2024/7/18
 */
public enum CommonErrorEnum implements CommonEnumShark {

    /**
     * 请求参数错误
     */
    REQUEST_PARAM_ERROR(400, "request params error", "trip.biz.im.request_param_error"),

    /**
     * 调用soa服务失败
     */
    DEPENDENCY_SOA_SERVICE_ERROR(401, "Dependency soa service error",
        "trip.biz.im.dependency_soa_service_error"),


    /**
     * 无权限
     */
    NO_PERMISSION(91, "No permission",
            "trip.biz.im.no.permission"),
    ;

    /**
     * 错误码
     */
    private int errorCode;

    /**
     * 错误描述
     */
    private String errorMsg;

    /**
     * 友好提示的shark key
     */
    private String sharkKey;

    CommonErrorEnum(int errorCode, String errorMsg, String sharkKey) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.sharkKey = sharkKey;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public String getSharkKey() {
        return sharkKey;
    }
}

