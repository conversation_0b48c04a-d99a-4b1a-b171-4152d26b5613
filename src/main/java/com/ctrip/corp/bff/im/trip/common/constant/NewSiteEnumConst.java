package com.ctrip.corp.bff.im.trip.common.constant;


import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description  站点类型枚举
 * @author:  renyiwang
 * @Date: 2024/9/13
 */
public class NewSiteEnumConst {
    /**
     * 站点类型映射
     */
    public static final Map<String, Integer> SITE_MAP;

    public static final Map<String, String> TRAIN_SUB_LINE_MAP;

    static {
        Map<String, Integer> siteMap = new HashMap<>();
        /*
         * 机票
         */
        siteMap.put("N", 1);
        /*
         * 国际机票
         */
        siteMap.put("I", 1);
        /*
         * 酒店（大类，用于授权平台过渡使用，各产线不建议使用）
         */
        siteMap.put("C", 2);
        /*
         * 国内会员酒店
         */
        siteMap.put("C2M", 2);
        /*
         * 国内协议酒店
         */
        siteMap.put("M", 2);
        /*
         * 火车票
         */
        siteMap.put("T", 3);
        /**
         * 国际打车
         */
        siteMap.put("CAR_TAXI_INTL", 4);

        /**
         * 国际打车
         */
        siteMap.put("1", 4);

        /**
         * 接送机
         */
        siteMap.put("3", 4);

        /**
         * 境外火车
         */
        siteMap.put("OVERSEA_TRAIN", 3);

        /**
         * 外采日铁
         */
        siteMap.put("OUTSIDE_JP_TRAIN", 3);

        // 不可变集合
        SITE_MAP = Collections.unmodifiableMap(siteMap);


        Map<String, String> trainSubLineMap = new HashMap<>();
        /*
         * 火车票
         */
        trainSubLineMap.put("T", "TRAIN");
        /**
         * 境外火车
         */
        trainSubLineMap.put("OVERSEA_TRAIN", "INTL_TRAIN");

        /**
         * 外采日铁
         */
        trainSubLineMap.put("OUTSIDE_JP_TRAIN", "INTL_TRAIN");
        TRAIN_SUB_LINE_MAP = Collections.unmodifiableMap(trainSubLineMap);
    }
}
