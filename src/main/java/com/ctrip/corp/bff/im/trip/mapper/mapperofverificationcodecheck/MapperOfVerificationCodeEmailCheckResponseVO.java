package com.ctrip.corp.bff.im.trip.mapper.mapperofverificationcodecheck;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailCheckResponseType;
import com.ctrip.corp.bff.im.trip.common.constant.VerificationCodeCheckConstant;
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckResponseVO;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Component
public class MapperOfVerificationCodeEmailCheckResponseVO
    extends AbstractMapper<Tuple1<VerificationCodeEmailCheckResponseType>, VerificationCodeEmailCheckResponseVO> {
    @Override
    protected VerificationCodeEmailCheckResponseVO convert(Tuple1<VerificationCodeEmailCheckResponseType> tuple1) {
        VerificationCodeEmailCheckResponseType response = tuple1.getT1();
        boolean checkSuccess = false;
        VerificationCodeEmailCheckResponseVO result = new VerificationCodeEmailCheckResponseVO();
        if (response != null) {
            checkSuccess = BooleanUtils.toBoolean(response.getCheckResult());
            result.setCheckToken(response.getCheckToken());
            result.setFriendlyMessage(buildFriendlyMessage(checkSuccess, response));
        }
        result.setCheckSuccess(BooleanUtil.parseStr(checkSuccess));
        return result;
    }

    private String buildFriendlyMessage(boolean checkSuccess, VerificationCodeEmailCheckResponseType response) {
        if (checkSuccess) {
            return null;
        }
        return BFFSharkUtil.getSharkValue(String.format(VerificationCodeCheckConstant.CHECK_MESSAGE_FAIL, "default"));
    }

    @Override
    protected ParamCheckResult check(Tuple1<VerificationCodeEmailCheckResponseType> tuple1) {
        return null;
    }
}
