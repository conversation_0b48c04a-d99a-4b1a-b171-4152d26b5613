package com.ctrip.corp.bff.im.trip.mapper.mapperofimtripinit;

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.im.contract.ImTripInitRequestType;
import com.ctrip.corp.bff.im.trip.contract.ImTripInitRequestVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: z.c. wang
 * @Date: 2025/1/9 17:21
 * @Version 1.0
 */
@Component
public class MapperOfImTripInitRequest extends AbstractMapper<Tuple1<ImTripInitRequestVO>, ImTripInitRequestType> {
    @Override
    protected ImTripInitRequestType convert(Tuple1<ImTripInitRequestVO> tuple) {
        ImTripInitRequestVO imTripInitRequestVO = tuple.getT1();
        ImTripInitRequestType imTripInitRequest = new ImTripInitRequestType();
        imTripInitRequest.setIntegrationSoaRequestType(SoaRequestUtil.convertVo2IntegrationRequest(imTripInitRequestVO.getRequestHeader()));
        imTripInitRequest.setStrategyInfos(buildStrategyInfo(imTripInitRequestVO));
        imTripInitRequest.setOrderId(Optional.ofNullable(imTripInitRequestVO.getOrderId()).map(Long::valueOf).orElse(null));
        imTripInitRequest.setPageCode(imTripInitRequestVO.getPageCode());
        return imTripInitRequest;
    }

    private List<StrategyInfo> buildStrategyInfo(ImTripInitRequestVO imTripInitRequestVO) {
        List<StrategyInfo> strategyInfos = new ArrayList<>();
        StrategyInfo strategyInfoProduct = new StrategyInfo();
        strategyInfoProduct.setStrategyKey("PRODUCT");
        strategyInfoProduct.setStrategyValue(imTripInitRequestVO.getProductLine());
        strategyInfos.add(strategyInfoProduct);

        StrategyInfo strategyInfoScene = new StrategyInfo();
        strategyInfoScene.setStrategyKey("SCENE");
        strategyInfoScene.setStrategyValue(imTripInitRequestVO.getScene());
        strategyInfos.add(strategyInfoScene);

        return strategyInfos;
    }

    @Override
    protected ParamCheckResult check(Tuple1<ImTripInitRequestVO> tuple) {
        return null;
    }
}
