package com.ctrip.corp.bff.im.trip.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.im.trip.common.enums.CommonErrorEnum;
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoQueryRequestVO;
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoQueryResponseVO;
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfCustomerInfoQuery;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: z.c. wang
 * @Description 联系信息接口
 * @Date: 2025/5/9 14:04
 * @Version 1.0
 */
@WebService(name = "customerInfoQuery")
public class ServiceOfCustomerInfoQuery extends
        AbstractSyncService<CustomerInfoQueryRequestVO, CustomerInfoQueryResponseVO> {

    @Autowired
    private ProcessorOfCustomerInfoQuery processorOfCustomerInfoQuery;

    @Override
    public void validateRequest(CustomerInfoQueryRequestVO request) throws BusinessException {
        if (request == null
                || request.getRequestHeader() == null
                || request.getRequestHeader().getHeader() == null
                || StringUtil.isBlank(request.getRequestHeader().getHeader().getUserId())
                || StringUtil.isBlank(request.getRequestHeader().getHeader().getCorpId())) {
            throw BusinessExceptionBuilder.createAlertException(
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorMsg(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getSharkValue(),
                    CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode() + "");
        }
    }

    @Override
    protected Processor<CustomerInfoQueryRequestVO, CustomerInfoQueryResponseVO> getProcessor(CustomerInfoQueryRequestVO request) {
        return processorOfCustomerInfoQuery;
    }
}
