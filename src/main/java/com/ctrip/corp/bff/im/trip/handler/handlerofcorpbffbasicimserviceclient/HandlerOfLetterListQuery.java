package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.im.contract.LetterListQueryRequestType;
import com.ctrip.corp.bff.im.contract.LetterListQueryResponseType;
import com.ctrip.corp.bff.im.contract.CorpBffBasicImServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class HandlerOfLetterListQuery extends AbstractHandlerOfSOA<LetterListQueryRequestType, LetterListQueryResponseType, CorpBffBasicImServiceClient> {

    @Override
    protected String getMethodName() {
        return "letterListQuery";
    }
}
