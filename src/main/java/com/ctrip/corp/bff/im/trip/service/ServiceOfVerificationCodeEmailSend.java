package com.ctrip.corp.bff.im.trip.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.im.ct.contract.EmailCodeSendRequestVO;
import com.ctrip.corp.bff.im.ct.contract.EmailCodeSendResponseVO;
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfEmailCodeSend;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
@WebService(name = "emailCodeSend", enableEncryptDecryptAop = true)
public class ServiceOfVerificationCodeEmailSend
    extends AbstractSyncService<EmailCodeSendRequestVO, EmailCodeSendResponseVO> {
    @Autowired
    private ProcessorOfEmailCodeSend processor;

    @Override
    public void validateRequest(EmailCodeSendRequestVO request) throws BusinessException {

    }

    @Override
    protected Processor<EmailCodeSendRequestVO, EmailCodeSendResponseVO> getProcessor(
            EmailCodeSendRequestVO request) {
        return processor;
    }
}
