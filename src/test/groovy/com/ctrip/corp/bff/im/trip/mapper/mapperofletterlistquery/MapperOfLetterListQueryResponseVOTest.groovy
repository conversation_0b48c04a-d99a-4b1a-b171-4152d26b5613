package com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery

import com.ctrip.corp.bff.framework.template.common.language.LanguageUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.im.contract.CountInfo
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType
import com.ctrip.corp.bff.im.contract.LetterInfo
import com.ctrip.corp.bff.im.contract.LetterListQueryResponseType
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryResponseVO
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum
import org.mockito.Mockito
import spock.lang.*

/**
 * <AUTHOR>
 * @date 2024/9/18
 */
class MapperOfLetterListQueryResponseVOTest extends Specification {
    MapperOfLetterListQueryResponseVO mapperOfLetterListQueryResponseVO = new MapperOfLetterListQueryResponseVO
            (
            )


    def setup() {

    }


    def "test convert null"() {
        when:
        LetterListQueryResponseVO result = mapperOfLetterListQueryResponseVO.convert(Tuple2.of(null, null))

        then:
        result != null
    }

    def "test convert"() {
        given:
        Mockito.mockStatic(LanguageUtil.class)
        Mockito.when(LanguageUtil.getLocale()).thenReturn(LanguageLocaleEnum.EN_US)
        when:

        LetterListQueryResponseVO result = mapperOfLetterListQueryResponseVO.convert(Tuple2.of(new LetterListQueryResponseType(letterInfoList: [new LetterInfo(
                createTime: "2024-09-18 11:11:11",
                subProductLine: "T",
                messageType: "AUTHORIZATION",
                contentType: "normalMessage")]),
                new CountInfoQueryResponseType(countInfos: [new CountInfo(key: "UnreadMessage", count: 1)])))

        then:
        result != null
    }

    def "test get Product Line"() {
        when:
        Integer result = mapperOfLetterListQueryResponseVO.getProductLine("")

        then:
        result == 0
    }

    def "test check"() {
        when:
        ParamCheckResult result = mapperOfLetterListQueryResponseVO.check(null)

        then:
        result == null
    }
}