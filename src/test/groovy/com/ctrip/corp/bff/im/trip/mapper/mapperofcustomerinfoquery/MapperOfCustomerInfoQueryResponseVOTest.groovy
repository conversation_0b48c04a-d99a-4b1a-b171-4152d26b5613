package com.ctrip.corp.bff.im.trip.mapper.mapperofcustomerinfoquery

import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.CustomerInfo
import com.ctrip.corp.bff.im.contract.CustomerInfoQueryResponseType
import com.ctrip.corp.bff.im.contract.VoipInfo
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoVO
import spock.lang.Specification

/**
 * @Author: z.c. wang
 * @Date: 2025/5/9 14:25
 * @Version 1.0
 */
class MapperOfCustomerInfoQueryResponseVOTest extends Specification {

    def mapper = new MapperOfCustomerInfoQueryResponseVO()

    def "Convert"() {
        given:
        def customerInfoQueryResponseType = new CustomerInfoQueryResponseType(
                customerInfos: [new CustomerInfo(
                        country: "China",
                        voipInfo: new VoipInfo(
                                voipDomain: "voipDomain",
                                voipNumber: "voipNumber",
                                voipPassword: "voipPassword",
                                voipUserName: "voipUserName"
                        ),
                        phoneInfo: new PhoneInfo(
                                phoneNo: "phoneNo",
                                transferPhoneNo: "transferPhoneNo",
                                areaCode: "86",
                                countryCode: "CN"
                        )
                )]
        )
        def result
        when:
        result = mapper.map(Tuple1.of(null))
        then:
        result.getCustomerInfos() == null

        when:
        result = mapper.map(Tuple1.of(customerInfoQueryResponseType))
        then:
        result.getCustomerInfos().size() == 1
        def customerInfoVO = result.getCustomerInfos().get(0)
        customerInfoVO.getCountry() == "China"
        customerInfoVO.getVoipInfo().getVoipDomain() == "voipDomain"
        customerInfoVO.getVoipInfo().getVoipNumber() == "voipNumber"
        customerInfoVO.getVoipInfo().getVoipPassword() == "voipPassword"
        customerInfoVO.getVoipInfo().getVoipUserName() == "voipUserName"
        customerInfoVO.getPhoneInfo().getPhoneNo() == "phoneNo"
        customerInfoVO.getPhoneInfo().getTransferPhoneNo() == "transferPhoneNo"
        customerInfoVO.getPhoneInfo().getAreaCode() == "86"
        customerInfoVO.getPhoneInfo().getCountryCode() == "CN"
    }
}
