package com.ctrip.corp.bff.im.trip.mapper.mapperofcustomerinfoquery

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoQueryRequestVO
import spock.lang.Specification

/**
 * @Author: z.c. wang
 * @Date: 2025/5/9 14:23
 * @Version 1.0
 */
class MapperOfCustomerInfoQueryRequestTest extends Specification {

    def mapper = new MapperOfCustomerInfoQueryRequest()

    def "Convert"() {
        when:
        def result = mapper.map(Tuple1.of(
                new CustomerInfoQueryRequestVO(requestHeader: new TemplateSoaRequestType())
        ))
        then:
        result != null
    }
}
