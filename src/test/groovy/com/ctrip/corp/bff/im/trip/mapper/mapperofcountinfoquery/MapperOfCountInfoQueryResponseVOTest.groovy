package com.ctrip.corp.bff.im.trip.mapper.mapperofcountinfoquery

import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.CountInfo
import com.ctrip.corp.bff.im.contract.CountInfoQueryResponseType
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryResponseVO
import spock.lang.*

/**
 * <AUTHOR>
 * @date 2024/9/18
 */
class MapperOfCountInfoQueryResponseVOTest extends Specification {
    MapperOfCountInfoQueryResponseVO mapperOfCountInfoQueryResponseVO = new MapperOfCountInfoQueryResponseVO
            (
            )


    def setup() {

    }


    def "test convert"() {
        when:
        CountInfoQueryResponseVO result = mapperOfCountInfoQueryResponseVO.convert(Tuple1.of(null))

        then:
        result == null
    }


    def "test convert1"() {
        when:
        CountInfoQueryResponseVO result = mapperOfCountInfoQueryResponseVO.convert(Tuple1.of(
                new CountInfoQueryResponseType(
                        countInfos: [new CountInfo(
                                count: 1,
                                key: "UnreadMessage")])))

        then:
        result != null
    }

    def "test check"() {
        when:
        ParamCheckResult result = mapperOfCountInfoQueryResponseVO.check(null)

        then:
        result == null
    }
}