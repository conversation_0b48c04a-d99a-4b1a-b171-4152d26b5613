package com.ctrip.corp.bff.im.trip.mapper.mapperofcountinfoquery

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.CountInfoQueryRequestType
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryRequestVO
import spock.lang.*

/**
 * <AUTHOR>
 * @date 2024/9/18
 */
class MapperOfCountInfoQueryRequestTypeTest extends Specification {
    MapperOfCountInfoQueryRequestType mapperOfCountInfoQueryRequestType = new MapperOfCountInfoQueryRequestType
            (
            )


    def setup() {

    }


    def "test convert"() {
        when:
        CountInfoQueryRequestType result = mapperOfCountInfoQueryRequestType.convert(Tuple1.of(
                new CountInfoQueryRequestVO(
                        requestHeader: new TemplateSoaRequestType(),
                        queryKeys: ["UnreadMessage"])))

        then:
        result != null
    }

    def "test check"() {
        when:
        ParamCheckResult result = mapperOfCountInfoQueryRequestType.check(null)

        then:
        result == null
    }
}