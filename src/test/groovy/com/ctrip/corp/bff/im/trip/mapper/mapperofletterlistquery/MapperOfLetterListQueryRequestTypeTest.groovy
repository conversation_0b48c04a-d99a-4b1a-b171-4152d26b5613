package com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.LetterListQueryRequestType
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryRequestVO
import spock.lang.*

/**
 * <AUTHOR>
 * @date 2024/9/18
 */
class MapperOfLetterListQueryRequestTypeTest extends Specification {
    MapperOfLetterListQueryRequestType mapperOfLetterListQueryRequestType = new MapperOfLetterListQueryRequestType
            (
            )


    def setup() {

    }


    def "test convert"() {
        when:
        LetterListQueryRequestType result = mapperOfLetterListQueryRequestType.convert(Tuple1.of(
                new LetterListQueryRequestVO(requestHeader: new TemplateSoaRequestType(), pageIndex: 1)))

        then:
        result != null
    }

    def "test check"() {
        when:
        ParamCheckResult result = mapperOfLetterListQueryRequestType.check(null)

        then:
        result == null
    }
}