package com.ctrip.corp.bff.im.trip.service

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.im.trip.contract.ImTripInitRequestVO
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfImTripInit
import com.ctrip.corp.frontend.BaseSpec


/**
 * <AUTHOR>
 * @date 2025/3/20
 */
class ServiceOfImTripInitTest extends BaseSpec {

    private ProcessorOfImTripInit processorOfImTripInit = Mock()

    def serviceOfImTripInit = new ServiceOfImTripInit(
            processorOfImTripInit: processorOfImTripInit
    )

    def "test validateRequest when imTripInitRequestVO is invalid"() {
        when:
        serviceOfImTripInit.validateRequest(null)

        then:
        def exception = thrown(BusinessException)
        with(exception) {
            errorCode == 401
            errorMessage == "request is invalid"
        }

        where:
        imTripInitRequestVO << [
                null,
                new ImTripInitRequestVO(),
                new ImTripInitRequestVO(requestHeader: new TemplateSoaRequestType()),
        ]
    }

    def "test validateRequest when orderId is invalid"() {
        given:
        ImTripInitRequestVO imTripInitRequestVO = Mock()
        TemplateSoaRequestType requestHeader = Mock()
        TemplateHeader header = Mock()

        and: "mock imTripInitRequestVO"
        imTripInitRequestVO.getRequestHeader() >> requestHeader
        imTripInitRequestVO.getOrderId() >> orderId

        and: "mock requestHeader"
        requestHeader.getHeader() >> header

        when:
        serviceOfImTripInit.validateRequest(imTripInitRequestVO)

        then:
        def exception = thrown(BusinessException)
        with(exception) {
            errorCode == 402
            errorMessage == "orderId is invalid"
        }

        where:
        orderId << ["abc", "1ac"]
    }

}
