package com.ctrip.corp.bff.im.trip.processor

import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.ImTripInitRequestType
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType
import com.ctrip.corp.bff.im.trip.contract.ImTripInitRequestVO
import com.ctrip.corp.bff.im.trip.contract.ImTripInitResponseVO
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient.HandlerOfImTripInit
import com.ctrip.corp.bff.im.trip.mapper.mapperofimtripinit.MapperOfImTripInitRequest
import com.ctrip.corp.bff.im.trip.mapper.mapperofimtripinit.MapperOfImTripInitResponseVO
import com.ctrip.corp.frontend.BaseSpec
import mockit.Mock
import mockit.MockUp

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
class ProcessorOfImTripInitTest extends BaseSpec {

    private MapperOfImTripInitRequest mapperOfImTripInitRequest = Mock()
    private HandlerOfImTripInit handlerOfImTripInit = Mock()
    private MapperOfImTripInitResponseVO mapperOfImTripInitResponseVO = Mock()

    def processorOfImTripInit = new ProcessorOfImTripInit(
            mapperOfImTripInitRequest: mapperOfImTripInitRequest,
            handlerOfImTripInit: handlerOfImTripInit,
            mapperOfImTripInitResponseVO: mapperOfImTripInitResponseVO
    )

    def "test execute when checkRequest failed"() {
        given:
        ImTripInitRequestVO imTripInitRequestVO = Mock()
        ImTripInitResponseVO imTripInitResponseVO = Mock()

        and: "mock private method"
        new MockUp<ProcessorOfImTripInit>() {
            @Mock
            boolean checkRequest(ImTripInitRequestVO requestVar) {
                return false
            }
        }

        and: 'mock mapperOfImTripInitRequest'
        mapperOfImTripInitResponseVO.map(_ as Tuple1) >> imTripInitResponseVO

        expect:
        processorOfImTripInit.execute(imTripInitRequestVO) == imTripInitResponseVO
    }

    def "test execute when checkRequest success"() {
        given:
        ImTripInitRequestVO imTripInitRequestVO = Mock()
        ImTripInitResponseVO imTripInitResponseVO = Mock()
        ImTripInitRequestType imTripInitRequestType = Mock()
        ImTripInitResponseType imTripInitResponseType = Mock()

        and: "mock private method"
        new MockUp<ProcessorOfImTripInit>() {
            @Mock
            boolean checkRequest(ImTripInitRequestVO requestVar) {
                return true
            }
        }

        and: "mock mapperOfImTripInitRequest"
        mapperOfImTripInitRequest.map(_ as Tuple1) >> imTripInitRequestType

        and: "mock handlerOfImTripInit"
        WaitFuture imTripInitWaitFuture = Mock()
        handlerOfImTripInit.execute(imTripInitRequestType) >> imTripInitWaitFuture
        imTripInitWaitFuture.get() >> imTripInitResponseType

        and: 'mock mapperOfImTripInitResponseVO'
        mapperOfImTripInitResponseVO.map(_ as Tuple1) >> imTripInitResponseVO

        expect:
        processorOfImTripInit.execute(imTripInitRequestVO) == imTripInitResponseVO
    }

    def "test checkRequest when request is null"() {
        expect:
        !processorOfImTripInit.checkRequest(null)
    }

    def "test checkRequest when requestHeader is null"() {
        expect:
        !processorOfImTripInit.checkRequest(new ImTripInitRequestVO())
    }

    def "test checkRequest when header is null"() {
        given:
        ImTripInitRequestVO imTripInitRequestVO = Mock()
        TemplateSoaRequestType requestHeader = Mock()

        and: "mock imTripInitRequestVO"
        imTripInitRequestVO.getRequestHeader() >> requestHeader

        expect:
        !processorOfImTripInit.checkRequest(imTripInitRequestVO)
    }

    def "test checkRequest when header is invalid"() {
        given:
        ImTripInitRequestVO imTripInitRequestVO = Mock()
        TemplateSoaRequestType requestHeader = Mock()
        TemplateHeader templateHeader = Mock()

        and: "mock imTripInitRequestVO"
        imTripInitRequestVO.getRequestHeader() >> requestHeader

        and: "mock requestHeader"
        requestHeader.getHeader() >> templateHeader

        and: "mock templateHeader"
        templateHeader.getUserId() >> userId
        templateHeader.getCorpId() >> corpId

        expect:
        !processorOfImTripInit.checkRequest(imTripInitRequestVO)

        where:
        userId | corpId
        null   | null
        ""     | null
        " "    | null
        null   | ""
        null   | " "
    }
}
