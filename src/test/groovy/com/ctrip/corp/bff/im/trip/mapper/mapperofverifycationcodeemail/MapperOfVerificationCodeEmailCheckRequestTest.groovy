package com.ctrip.corp.bff.im.trip.mapper.mapperofverifycationcodeemail

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailCheckRequestType
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckRequestVO
import com.ctrip.corp.bff.im.trip.mapper.mapperofverificationcodecheck.MapperOfVerificationCodeEmailCheckRequest
import spock.lang.Specification
import spock.lang.Unroll

class MapperOfVerificationCodeEmailCheckRequestTest extends Specification {
    def testObj = new MapperOfVerificationCodeEmailCheckRequest()

    @Unroll
    def "convertTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.convert(tuple1)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        tuple1 || expectedResult
        Tuple1.of(new VerificationCodeEmailCheckRequestVO(emailInfo: new EmailInfoVO(email: "email",transferEmail: "email"),verificationCode: "verificationCode")) || new VerificationCodeEmailCheckRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(), emailInfo: new EmailInfo(email: "email", transferEmail: "email"), verificationCode: "verificationCode")
    }

    @Unroll
    def "checkTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.check(tuple1)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        tuple1 || expectedResult
        null   || null
    }
}
