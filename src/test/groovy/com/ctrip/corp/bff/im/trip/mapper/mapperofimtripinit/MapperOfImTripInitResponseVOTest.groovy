package com.ctrip.corp.bff.im.trip.mapper.mapperofimtripinit

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.ImEntranceInfo
import com.ctrip.corp.bff.im.contract.ImTripInitResponseType
import com.ctrip.corp.bff.im.contract.Question
import com.ctrip.corp.bff.im.contract.QuestionInfo
import com.ctrip.corp.bff.im.contract.ServiceContractInfo
import com.ctrip.corp.bff.im.contract.ServiceEmailInfo
import com.ctrip.corp.bff.im.contract.ServicePhoneInfo
import com.ctrip.corp.bff.im.trip.contract.ImEntranceInfoVO
import com.ctrip.corp.bff.im.trip.contract.QuestionInfoVO
import com.ctrip.corp.bff.im.trip.contract.ServiceContractInfoVO
import com.ctrip.corp.frontend.BaseSpec
import mockit.Mock
import mockit.MockUp

/**
 * @author: xliu35
 * @date: 2025/3/20
 */
class MapperOfImTripInitResponseVOTest extends BaseSpec {

    def mapper = new MapperOfImTripInitResponseVO()

    def "test convert"() {
        given:
        ImTripInitResponseType imTripInitResponseType = Mock()

        ImEntranceInfoVO imEntranceInfoVO = Mock()
        ServiceContractInfoVO serviceContractInfoVO = Mock()
        QuestionInfoVO questionInfoVO = Mock()

        and: "mock imTripInitResponseType"
        imTripInitResponseType.getSceneType() >> "sceneType"

        and: "mock private method"
        new MockUp<MapperOfImTripInitResponseVO>() {
            @Mock
            List<ImEntranceInfoVO> buildImEntranceInfoList(List<ImEntranceInfo> imEntranceInfoListVar) {
                return [imEntranceInfoVO]
            }

            @Mock
            ServiceContractInfoVO buildServiceContractInfo(ServiceContractInfo serviceContractInfoVar) {
                return serviceContractInfoVO
            }

            @Mock
            List<QuestionInfoVO> buildQuestionInfoVO(List<QuestionInfo> questionInfoListVar) {
                return [questionInfoVO]
            }
        }

        when:
        def imTripInitResponseVO = mapper.convert(Tuple1.of(imTripInitResponseType))

        then:
        imTripInitResponseVO != null
        with(imTripInitResponseVO) {
            imEntranceInfoList == [imEntranceInfoVO]
            serviceContractInfo == serviceContractInfoVO
            questionInfoList == [questionInfoVO]
            sceneType == "sceneType"
        }
    }

    def "test buildQuestionInfoVO when questionInfoList is empty"() {
        expect:
        mapper.buildQuestionInfoVO(questionInfoList) == null

        where:
        questionInfoList << [null, []]
    }

    def "test buildQuestionInfoVO"() {
        given:
        QuestionInfo questionInfo1 = Mock()
        QuestionInfo questionInfo2 = Mock()

        Question question = Mock()

        and: "mock questionInfo"
        questionInfo1.getType() >> "type"
        questionInfo1.getQuestionList() >> [question]
        questionInfo2.getType() >> "type"

        and: "mock question"
        question.getKey() >> "key"
        question.getQuestion() >> "question"

        when:
        def questionInfoVOList = mapper.buildQuestionInfoVO([questionInfo1, questionInfo2])

        then:
        questionInfoVOList != null
        questionInfoVOList.size() == 1
        with(questionInfoVOList[0]) {
            type == "type"
            questionList != null
            questionList.size() == 1
            with(questionList[0]) {
                key == "key"
                it.question == "question"
            }
        }
    }

    def "test buildServiceContractInfo when serviceContractInfo is null"() {
        expect:
        mapper.buildServiceContractInfo(null) == null
    }

    def "test buildServiceContractInfo when serviceEmailInfoList and servicePhoneInfoList is empty"() {
        given:
        ServiceContractInfo serviceContractInfo = Mock()

        when:
        def serviceContractInfoVO = mapper.buildServiceContractInfo(serviceContractInfo)

        then:
        serviceContractInfoVO != null
        with(serviceContractInfoVO) {
            serviceEmailInfoList == null
            servicePhoneInfoList == null
        }
    }

    def "test buildServiceContractInfo"() {
        given:
        ServiceContractInfo serviceContractInfo = Mock()
        ServiceEmailInfo serviceEmailInfo = Mock()
        ServicePhoneInfo servicePhoneInfo = Mock()

        and: "mock serviceContractInfo"
        serviceContractInfo.getServiceEmailInfoList() >> [serviceEmailInfo]
        serviceContractInfo.getServicePhoneInfoList() >> [servicePhoneInfo]

        and: "mock serviceEmailInfo"
        serviceEmailInfo.getEmail() >> "email"

        and: "mock servicePhoneInfo"
        servicePhoneInfo.getPhone() >> "phone"
        servicePhoneInfo.getCountryCode() >> "countryCode"
        servicePhoneInfo.getCountryName() >> "countryName"

        when:
        def serviceContractInfoVO = mapper.buildServiceContractInfo(serviceContractInfo)

        then:
        serviceContractInfoVO != null
        with(serviceContractInfoVO) {
            serviceEmailInfoList != null
            serviceEmailInfoList.size() == 1
            with(serviceEmailInfoList[0]) {
                email == "email"
            }
            servicePhoneInfoList != null
            servicePhoneInfoList.size() == 1
            with(servicePhoneInfoList[0]) {
                phone == "phone"
                countryCode == "countryCode"
                countryName == "countryName"
            }
        }
    }

    def "test buildImEntranceInfoList when imEntranceInfoList is empty"() {
        expect:
        mapper.buildImEntranceInfoList(imEntranceInfoList) == null

        where:
        imEntranceInfoList << [null, []]
    }

    def "test buildImEntranceInfoList"() {
        given:
        ImEntranceInfo imEntranceInfo = Mock()

        and: "mock imEntranceInfo"
        imEntranceInfo.getType() >> "type"
        imEntranceInfo.getIcon() >> "icon"
        imEntranceInfo.getBizLine() >> "bizLine"

        and: "mock BFFSharkUtil"
        new MockUp<BFFSharkUtil>() {
            @Mock
            String getSharkValue(String key) {
                return key
            }
        }

        when:
        def imEntranceInfoVOList = mapper.buildImEntranceInfoList([imEntranceInfo])

        then:
        imEntranceInfoVOList != null
        imEntranceInfoVOList.size() == 1
        with(imEntranceInfoVOList[0]) {
            type == "type"
            name == "trip.biz.bff.common.im.trip.entrance.type"
            icon == "icon"
            bizLine == "bizLine"
        }
    }
}
