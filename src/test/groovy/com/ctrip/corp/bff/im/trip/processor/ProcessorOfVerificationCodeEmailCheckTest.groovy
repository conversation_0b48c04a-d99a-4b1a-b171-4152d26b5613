package com.ctrip.corp.bff.im.trip.processor

import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailCheckRequestType
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckRequestVO
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckResponseVO
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handlerofcorpbffbasicimserviceclient.HandlerOfVerificationCodeEmailCheck
import com.ctrip.corp.bff.im.trip.mapper.mapperofverificationcodecheck.MapperOfVerificationCodeEmailCheckRequest
import com.ctrip.corp.bff.im.trip.mapper.mapperofverificationcodecheck.MapperOfVerificationCodeEmailCheckResponseVO
import spock.lang.Specification
import spock.lang.Unroll

class ProcessorOfVerificationCodeEmailCheckTest extends Specification {
    def testObj = new ProcessorOfVerificationCodeEmailCheck()
    def mapperOfVerificationCodeEmailCheckRequest = Mock(MapperOfVerificationCodeEmailCheckRequest)
    def handlerOfVerificationCodeEmailCheck = Mock(HandlerOfVerificationCodeEmailCheck)
    def mapperOfVerificationCodeEmailCheckResponseVO = Mock(MapperOfVerificationCodeEmailCheckResponseVO)

    def setup() {

        testObj.mapperOfVerificationCodeEmailCheckRequest = mapperOfVerificationCodeEmailCheckRequest
        testObj.handlerOfVerificationCodeEmailCheck = handlerOfVerificationCodeEmailCheck
        testObj.mapperOfVerificationCodeEmailCheckResponseVO = mapperOfVerificationCodeEmailCheckResponseVO
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        mapperOfVerificationCodeEmailCheckRequest.map(_) >> new VerificationCodeEmailCheckRequestType()
        handlerOfVerificationCodeEmailCheck.handleAsync(_) >> Mock(WaitFuture)

        when:
        def result = testObj.execute(request)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                                   || expectedResult
        new VerificationCodeEmailCheckRequestVO() || null
    }

    @Unroll
    def "trackingTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.tracking(request, response)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                                                                     | response                                                                     || expectedResult
        new VerificationCodeEmailCheckRequestVO() | new VerificationCodeEmailCheckResponseVO() || Collections.emptyMap()
    }
}
