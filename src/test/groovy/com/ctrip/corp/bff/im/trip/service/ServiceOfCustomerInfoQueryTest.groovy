package com.ctrip.corp.bff.im.trip.service

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.im.contract.CustomerInfoQueryRequestType
import com.ctrip.corp.bff.im.trip.common.enums.CommonErrorEnum
import com.ctrip.corp.bff.im.trip.contract.CustomerInfoQueryRequestVO
import spock.lang.Specification

/**
 * @Author: z.c. wang
 * @Date: 2025/5/15 20:06
 * @Version 1.0
 */
class ServiceOfCustomerInfoQueryTest extends Specification {

    def service = new ServiceOfCustomerInfoQuery()

    def "validateRequest"() {
        when:
        service.validateRequest(null)
        then:
        def e = thrown(BusinessException)
        e.errorCode == CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode()

        when:
        service.validateRequest(new CustomerInfoQueryRequestVO())
        then:
        e = thrown(BusinessException)
        e.errorCode == CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode()

        when:
        service.validateRequest(new CustomerInfoQueryRequestVO(
                requestHeader: new TemplateSoaRequestType()
        ))
        then:
        e = thrown(BusinessException)
        e.errorCode == CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode()

        when:
        service.validateRequest(new CustomerInfoQueryRequestVO(
                requestHeader: new TemplateSoaRequestType(
                        header: new TemplateHeader()
                )
        ))
        then:
        e = thrown(BusinessException)
        e.errorCode == CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode()

        when:
        service.validateRequest(new CustomerInfoQueryRequestVO(
                requestHeader: new TemplateSoaRequestType(
                        header: new TemplateHeader(
                                userId: "123"
                        )
                )
        ))
        then:
        e = thrown(BusinessException)
        e.errorCode == CommonErrorEnum.REQUEST_PARAM_ERROR.getErrorCode()
    }

}
