package com.ctrip.corp.bff.im.trip.processor

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryRequestVO
import com.ctrip.corp.bff.im.trip.contract.LetterListQueryResponseVO
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.HandlerOfCountInfoQuery
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.HandlerOfLetterListQuery
import com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery.MapperOfCountInfoQueryRequest
import com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery.MapperOfLetterListQueryRequestType
import com.ctrip.corp.bff.im.trip.mapper.mapperofletterlistquery.MapperOfLetterListQueryResponseVO
import spock.lang.*

/**
 * <AUTHOR>
 * @date 2024/9/18
 */
class ProcessorOfLetterListQueryTest extends Specification {
    MapperOfLetterListQueryResponseVO mapperOfLetterListQueryResponseVO = Mock()
    HandlerOfLetterListQuery handlerOfLetterListQuery = Mock()
    HandlerOfCountInfoQuery handlerOfCountInfoQuery = Mock()
    MapperOfLetterListQueryRequestType mapperOfLetterListQueryRequestType = Mock()
    MapperOfCountInfoQueryRequest mapperOfCountInfoQueryRequest = Mock()
    ProcessorOfLetterListQuery processorOfLetterListQuery = new ProcessorOfLetterListQuery
            (
                    mapperOfLetterListQueryResponseVO: mapperOfLetterListQueryResponseVO,
                    handlerOfLetterListQuery: handlerOfLetterListQuery,
                    handlerOfCountInfoQuery: handlerOfCountInfoQuery,
                    mapperOfLetterListQueryRequestType: mapperOfLetterListQueryRequestType,
                    mapperOfCountInfoQueryRequestType: mapperOfCountInfoQueryRequest
            )


    def setup() {

    }


    def "test execute"() {
        given:
        handlerOfLetterListQuery.handleAsync(_) >> Mock(WaitFuture)
        handlerOfCountInfoQuery.handleAsync(_) >> Mock(WaitFuture)
        when:
        LetterListQueryResponseVO result = processorOfLetterListQuery.execute(new LetterListQueryRequestVO(
                requestHeader: new TemplateSoaRequestType()))

        then:
        result == null
    }

    def "test tracking"() {
        when:
        Map<String, String> result = processorOfLetterListQuery.tracking(null, null)

        then:
        result == null
    }
}