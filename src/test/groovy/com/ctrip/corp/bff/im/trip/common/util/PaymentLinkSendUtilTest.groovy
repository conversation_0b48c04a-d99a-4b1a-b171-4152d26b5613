package com.ctrip.corp.bff.im.trip.common.util

import com.ctrip.soa.corp.order.orderindexservice.v1.AirportProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.AmountInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.ApprovalLevelDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.ApprovalOrderDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.ApprovalUserDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.AttachmentInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.BusProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.CharterProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.ContactInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.CorpDockingInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.CostCenterCustomFieldDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.CostCenterDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.CostCenterExtendDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.FlightProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.FlightStopInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.HotelDailyRoomPriceInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.HotelProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.IntlTrainProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.MiceProductInfo
import com.ctrip.soa.corp.order.orderindexservice.v1.OfflineValueAddedProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderAggregateProcessInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderBasicInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderConfigsSnapshotInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderCostCenterDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderFinalTripInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderFlightChangeInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderFlightTicketInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderNewRebookInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderRebookInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderRefundInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderRelationInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OtherFeeInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OutSideTrainInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.PassengerCostCenterDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.PassengerInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.PassengerSeatInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.ProductExtendInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.ProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.ReasonCodeInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.RebookFlightInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.RebookTrainInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.RefundSettlementDetailType
import com.ctrip.soa.corp.order.orderindexservice.v1.RentalProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.RequirementProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.SalesQuotationProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.TaxiChoosedVehicleInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.TaxiProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.TourProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.TourVisaProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.TrainProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.TravelControlPreApprovalType
import com.ctrip.soa.corp.order.orderindexservice.v1.ValueAddedCharterProductInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.VasBasicInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.VisaProductInfoType
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*

class PaymentLinkSendUtilTest  extends Specification {

    def "test need Manual Send Message"() {
        when:
boolean result = PaymentLinkSendUtil.needManualSendMessage(new OrderIndexGetResponseType(new ResponseStatusType(), 0, "responseDesc", new OrderDetailInfoType(new OrderBasicInfoType(1l, 0, "subProductLine", "corpId", "agreementSignCompany", 1l, "uid", "policyUid", "eid", "corpPayType", 0, 0, "orderStatus", "displayOrderStatus", "approvalStatus", "orderDate", "pos", "orderDateUTC", "useDate", "useDateUTC", "cancelDate", "paymentType", 0 as BigDecimal, 0 as BigDecimal, "currency", 0 as BigDecimal, 1l, "bookingChannel", "channel", "secondChannel", "orderLanguage", "tripType", "balanceType", Boolean.TRUE, "losslessCancelTime", "losslessCancelTimeUTC", Boolean.TRUE, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, Boolean.TRUE, "userType", 0 as BigDecimal, "operatingMode", "customerBrand", Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, "corpGroupId", Boolean.TRUE, "personalPayStatus", new VasBasicInfoType("orderType", "subOrderType", "trackingNo", "trackingCompany", Boolean.TRUE, "refundChangePolicy"), 0 as BigDecimal, 0 as BigDecimal, "employeeId", "realPayCurrency", 0 as BigDecimal, new AmountInfoType(0 as BigDecimal, "currency", 0 as BigDecimal), "reimbursementStatus", 1l, "partnerEid", "partnerId", "resellerPartnerId", "resellerSource"), [new ProductInfoType(0, "departureTime", "departureTimeUTC", "arrivalTime", "arrivalTimeUTC", 0, "departureCityName", 0, "arrivalCityName", 0, 0, "departureAddress", "arrivalAddress", Boolean.TRUE, "flightNo", "classGrade", Boolean.TRUE, "pnr", 0, 0, "hotelName", "bedType", 0, 0, Boolean.TRUE, 0, 1l, "trainNo", 0, 0, "vendorName", 0, "vehicleName", 0, 0, 0, "busNo", [new ProductExtendInfoType("name", "value", "type")], new FlightProductInfoType(0, 0, "flightNo", "airLineName", "classGrade", "subClass", "pnr", 0, "craftTypeId", "craftTypName", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "customerId", "agreementType", "shareFlightNo", "directFlightChannel", "officeNo", 0, 0, 0 as BigDecimal, 0, 0, Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, 0, 0, "departureAirportBuildingName", "departureAirportSuperShortName", "departureAirportTerminalName", 0, "arrivalAirportBuildingName", "arrivalAirportSuperShortName", "arrivalAirportTerminalName", "departureAirportCode", "departureAirportName", "arrivalAirportCode", "arrivalAirportName", "departureCityCode", "arrivalCityCode", "specialPriceType", [new FlightStopInfoType(1l, 1l, "airportCode", 0, "stopCityName", 0, "arrivalTime", "arrivalTimeUTC", "departureTime", "departureTimeUTC")], "shareCompanyName", 0, 0), new HotelProductInfoType(0, "vendorId", 0, "hotelName", "hotelNameEn", "hotelAddress", 0, 1l, "roomName", 0, "basicRoomName", 0, 0, "bedType", 0, 0, Boolean.TRUE, 0, "confirmNo", "coordinateCode", "latitude", "longitude", "earlyArrivalTime", "earlyArrivalTimeUTC", "lastArrivalTime", "lastArrivalTimeUTC", "gdsType", "paymentGuaranteeFlag", [new HotelDailyRoomPriceInfoType(1l, "arrivalTime", "departureTime", 0, 0, 0 as BigDecimal, "payCurrency", 0 as BigDecimal, 0 as BigDecimal, "settlementCurrency", 0 as BigDecimal, 0 as BigDecimal, "originalCurrency", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal)], "latestDepartureTime", "latestDepartureTimeUTC", 0 as BigDecimal), new TrainProductInfoType("trainNo", 0, "ticketEntrance", 0, 0, "departureStationName", "arrivalStationName", "departureStationCode", "arrivalStationCode"), new AirportProductInfoType(0, "vehicleName", "vendorId", "vendorName", "vendorOrderId", "productNo", 0, 0, "fixedLocationCode", 0, 0, "driverName", "carBrand", "carPlateNumber", "carColor", "departureLongitude", "departureLatitude", "arrivalLongitude", "arrivalLatitude", "departureAddress", "arrivalAddress", "departureAddressDetail", "arrivalAddressDetail", "serviceBeginTime", "serviceEndTime"), new RentalProductInfoType(0, "vehicleName", "vendorId", "vendorName", "vendorOrderId", "departureLongitude", "departureLatitude", "arrivalLongitude", "arrivalLatitude", "departureAddress", "arrivalAddress", "departureAddressDetail", "arrivalAddressDetail"), new TaxiProductInfoType(0, "vehicleName", "vendorId", "vendorName", "vendorOrderId", "driverName", "carBrand", "carColor", "carPlateNumber", 0, "departureLongitude", "departureLatitude", "arrivalLongitude", "arrivalLatitude", "departureAddress", "arrivalAddress", "departureAddressDetail", "arrivalAddressDetail", [new TaxiChoosedVehicleInfoType(0, "vehicleName", "vendorId", "vendorName", 0, 0 as BigDecimal)], "serviceBeginTime", "serviceEndTime", "standardId", "tripVendorOrderId"), new CharterProductInfoType(0, "vehicleName", "vendorId", "vendorName", "vendorOrderId", 0, 0 as BigDecimal, "driverName", "carBrand", "carColor", 0, 0, "carPlateNumber", "departureLongitude", "departureLatitude", "arrivalLongitude", "arrivalLatitude", "departureAddress", "arrivalAddress", "departureAddressDetail", "arrivalAddressDetail"), new BusProductInfoType("vendorOrderId", "busNo", "departureStationName", "arrivalStationName"), new IntlTrainProductInfoType(0, 0, "trainNo", "seatCode", "departureStationCode", "departureStationName", "arrivalStationCode", "arrivalStationName", "departureCountryCode", "departureCountryName", "arrivalCountryCode", "arrivalCountryName", "departureTime", "arrivalTime", "supplierCode", "departureTimeUTC", "arrivalTimeUTC"), new OutSideTrainInfoType(0, "trainNo", "trainName", "departureStationName", "arrivalStationName", 0 as BigDecimal, 0 as BigDecimal, "seatType", "departureTime", "arrivalTime", "trainType"), new VisaProductInfoType(0, "serviceContent"), "crossBoundaryFlag", new TourVisaProductInfoType(), "productName", [new RequirementProductInfoType("subProductLine", 0, 0, 0, 0, 0, "description")], new TourProductInfoType("productId", "productName", "productTypeCode", "productTypeName", 0, "productPatternName", "productAreaCode", "productAreaName", 0, "departureDate", "returnDate"), new ValueAddedCharterProductInfoType(0, 0, "departureLongitude", "departureLatitude", "departureAddress", "departureAddressDetail", "departureCoordinateType", "arrivalLongitude", "arrivalLatitude", "arrivalAddress", "arrivalAddressDetail", "arrivalCoordinateType", 1l, "vehicleName", "carColor", "plateNumber", "driverName", "driverPhone", "countryCode", 0 as BigDecimal), new OfflineValueAddedProductInfoType("productNo", "departureAddress", "arrivalAddress", "seatTypeName", "vehicleName", "insuranceName", "arrivalCountryId", "visaType", "entryCount"))], [new PassengerInfoType("passengerName", 0, "cardNo", "countryCode", "mobilePhone", "corpUserId", "infoId", "employeeId", "userType", "gender", "birthDay", "nationality", "passengerType", "email", 1l, "reimbursementStatus", 1l)], new ContactInfoType("contactName", "countryCode", "mobilePhone", "email"), [new OrderRelationInfoType(1l, "subProductLine", 1l)], [new OrderFinalTripInfoType(0, 0, "passengerName", 0, "cardNo", "productNo", "productLevel", "departureTime", "departureTimeUTC", "arrivalTime", "arrivalTimeUTC", "departureCountryCode", "departureCountry", "arrivalCountryCode", "arrivalCountry", 0, "departureCity", 0, "arrivalCity", 0, "departureStationCode", "departureAirportSuperShortName", "departureAirportTerminalName", "arrivalStationCode", "arrivalAirportSuperShortName", "arrivalAirportTerminalName", "departureAddress", "arrivalAddress", "shareFlightNo", 0, [new FlightStopInfoType(1l, 1l, "airportCode", 0, "stopCityName", 0, "arrivalTime", "arrivalTimeUTC", "departureTime", "departureTimeUTC")], "departureStationName", "arrivalStationName", "airLineName", Boolean.TRUE, "crossBoundaryFlag", "ticketStatus", "recordStatus", Boolean.TRUE, 0, "hotelName", "ticketEntrance", "shareCompanyName", 0, 0, 0, "departureStationNameCN", "arrivalStationNameCN", 0)], new CostCenterDetailInfoType("costCenterType", "costCenterDesc", "costCenterDescEn", [new OrderCostCenterDetailInfoType(0, "costCenterTitle", "costCenterTitleEn", "costCenterValue", "costCenterNo")], [new PassengerCostCenterDetailInfoType("corpUserId", "infoId", "passengerName", 0, "costCenterTitle", "costCenterTitleEn", "costCenterValue", "costCenterNo")], new CostCenterExtendDetailInfoType("journeyNo", "journeyNoTitle", "journeyNoTitleEn", "projectId", "projectCode", "project", "projectTitle", "projectTitleEn", "tripPurposeId", "tripPurposeCode", "tripPurpose", "tripPurposeTitle", "tripPurposeTitleEn", 0, [new CostCenterCustomFieldDetailInfoType("title", "titleEn", "value")])), [new TravelControlPreApprovalType("preApprovalNo", "subPreApprovalNo", "passengerName", "uid", 1l, "preApprovalResult", 0, "preApprovalType", "ticketChangeId", "externalEmployeeId")], new ApprovalOrderDetailInfoType("externalId", "scene", "approvalStatus", 0, "approvalDeadline", 1l, "ssoKey", 1l, Boolean.TRUE, Boolean.TRUE, "followedTripOrderExternalId", "preApprovalNo", "subPreApprovalNo", 0, Boolean.TRUE, "approvalVerifyId", 1l, Boolean.TRUE, Boolean.TRUE, 0, "rejectReason", [new ApprovalLevelDetailInfoType(0, "approvalStatus", 0, ["supportApprovalWayList"], [new ApprovalUserDetailInfoType("uid", "approvalWay", "approvalStatus", "approvalTime", "approvalTimeUTC")], [new ApprovalUserDetailInfoType("uid", "approvalWay", "approvalStatus", "approvalTime", "approvalTimeUTC")])], Boolean.TRUE, Boolean.TRUE, "ticketChangedId", "prepareApprovalControl", 0, 1l), [new OrderFlightChangeInfoType(1l, 0, "passengerName", 0, "changeStatus", 0, 0, "flightChangeReason", Boolean.TRUE, Boolean.TRUE, "rrStatus")], [new OrderFlightTicketInfoType(0, "passengerName", "ticketNo", "flightNo", "takeOffTime", "status", "ticketSignCode", 1l)], [new OrderRefundInfoType(1l, 0, "passengerName", "refundStatus", 0 as BigDecimal, 0 as BigDecimal, "refundApplyTime", "refundApplyTimeUTC", "refundFinishTime", "refundFinishTimeUTC", 1l)], [new OrderRebookInfoType(1l, 0, "passengerName", "rebookStatus", "rebookApplyTime", "rebookApplyTimeUTC", "rebookFinishTime", "rebookFinishTimeUTC", 0 as BigDecimal, 0 as BigDecimal, "departureTime", "departureTimeUTC", "arrivalTime", "arrivalTimeUTC", 0, "departureCityName", 0, "arrivalCityName", new RebookFlightInfoType("flightNo", "airlineCode", "classGrade", "departureAirportCode", "departureAirportName", "arrivalAirportCode", "arrivalAirportName", "departureAirportBuilding", "arrivalAirportBuilding", [new FlightStopInfoType(1l, 1l, "airportCode", 0, "stopCityName", 0, "arrivalTime", "arrivalTimeUTC", "departureTime", "departureTimeUTC")]), new RebookTrainInfoType("trainNo", 0, 0, "departureStationName", 0, "arrivalStationName"), 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal)], [new PassengerSeatInfoType(0, "passengerName", "coach", "seat")], [new CorpDockingInfoType("dockingOrderId", "dockingSource")], [new AttachmentInfoType("ticketChangedId", "scene", "attachmentId", "attachmentName", "attachmentUrl", "attachmentType", true, "extraValue")], new MiceProductInfo(0, "uid", "miceCorpId", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, new SalesQuotationProductInfoType(0, 1l, "purchaseRequestNo", "description", 0, "purchaseRequestUrl", 1l, "salesQuotationNo", "eid", "createDate", 1l), "miceUid"), [new OrderAggregateProcessInfoType("ticketChangedId", "scene", "token")], [new ReasonCodeInfoType(0, 0, 0, "reasonCode", "reasonCodeDesc", "customReasonCode", "customReasonDesc", "controlResultExtend", 0, 1l)], new OrderConfigsSnapshotInfoType("checkInRequiredApproval"), [new RefundSettlementDetailType("refundOrderId", 0 as BigDecimal, "currency", 0 as BigDecimal, "refundDetailId", 1l)], [new OtherFeeInfoType("feeType", 0 as BigDecimal, 0 as BigDecimal, "currency", "payStatus")], [new OrderNewRebookInfoType(1l, 1l, 1l)], [new com.ctrip.soa.corp.order.orderindexservice.v1.OrderAmountInfoType("amountType", 0 as java.math.BigDecimal, "currency", 0 as java.math.BigDecimal)])))

        then:
result == true    }

    def "test check Data"() {
        when:
PaymentLinkSendUtil.checkData(new com.ctrip.corp.bff.tools.contract.CheckDataResponseType(new com.ctriposs.baiji.rpc.common.types.ResponseStatusType(), new com.ctrip.corp.bff.framework.template.entity.IntegrationResponse("errorMessage", "errorCode", new com.ctrip.corp.bff.framework.template.entity.ActionInfo("actionType", [new com.ctrip.corp.bff.framework.template.entity.MapString("key", "value")])), [new com.ctrip.corp.bff.tools.contract.DataCheckResult("key", "type", "checkResult", "hintLevel", "verifyCode", "friendlyMessage")]))

        then:
false//todo - validate something
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme