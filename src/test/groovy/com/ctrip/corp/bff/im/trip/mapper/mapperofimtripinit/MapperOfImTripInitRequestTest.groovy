package com.ctrip.corp.bff.im.trip.mapper.mapperofimtripinit

import com.ctrip.corp.bff.framework.template.common.utils.soa.SoaRequestUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.trip.contract.ImTripInitRequestVO
import com.ctrip.corp.frontend.BaseSpec
import mockit.Mock
import mockit.MockUp
/**
 * @Author: xliu35
 * @Date: 2025/3/20
 */
class MapperOfImTripInitRequestTest extends BaseSpec {

    def mapper = new MapperOfImTripInitRequest()

    def "test convert"() {
        given:
        ImTripInitRequestVO imTripInitRequestVO = Mock()

        IntegrationSoaRequestType integrationSoaRequestType = Mock()
        StrategyInfo strategyInfo = Mock()

        and: "mock imTripInitRequestVO"
        imTripInitRequestVO.getOrderId() >> "123456"
        imTripInitRequestVO.getPageCode() >> "pageCode"

        and: 'mock SoaRequestUtil'
        new MockUp<SoaRequestUtil>() {
            @Mock
            IntegrationSoaRequestType convertVo2IntegrationRequest(TemplateSoaRequestType templateSoaRequestTypeVar) {
                return integrationSoaRequestType
            }
        }

        and: "mock private method"
        new MockUp<MapperOfImTripInitRequest>() {
            @Mock
            List<StrategyInfo> buildStrategyInfo(ImTripInitRequestVO navHeaderInitRequestVO) {
                return [strategyInfo]
            }
        }

        when:
        def imTripInitRequestType = mapper.convert(Tuple1.of(imTripInitRequestVO))

        then:
        imTripInitRequestType != null
        with(imTripInitRequestType) {
            it.integrationSoaRequestType == integrationSoaRequestType
            strategyInfos == [strategyInfo]
            orderId == 123456L
            pageCode == "pageCode"
        }
    }

    def "test buildStrategyInfo"(){
        given:
        ImTripInitRequestVO imTripInitRequestVO = Mock()

        and:"mock imTripInitRequestVO"
        imTripInitRequestVO.getProductLine() >> "productLine"
        imTripInitRequestVO.getScene() >> "scene"

        when:
        def strategyInfos = mapper.buildStrategyInfo(imTripInitRequestVO)

        then:
        strategyInfos!=null
        strategyInfos.size() == 2
        with(strategyInfos[0]){
            strategyKey == "PRODUCT"
            strategyValue == "productLine"
        }
        with(strategyInfos[1]){
            strategyKey == "SCENE"
            strategyValue == "scene"
        }
    }
}
