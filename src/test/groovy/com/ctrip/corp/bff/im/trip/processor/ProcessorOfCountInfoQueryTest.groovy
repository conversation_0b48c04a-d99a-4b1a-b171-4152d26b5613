package com.ctrip.corp.bff.im.trip.processor

import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.im.trip.contract.CountInfoQueryResponseVO
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.HandlerOfCountInfoQuery
import com.ctrip.corp.bff.im.trip.mapper.mapperofcountinfoquery.MapperOfCountInfoQueryRequestType
import com.ctrip.corp.bff.im.trip.mapper.mapperofcountinfoquery.MapperOfCountInfoQueryResponseVO
import spock.lang.*

/**
 * <AUTHOR>
 * @date 2024/9/18
 */
class ProcessorOfCountInfoQueryTest extends Specification {
    MapperOfCountInfoQueryRequestType mapperOfCountInfoQueryRequestType = Mock()
    HandlerOfCountInfoQuery handlerOfCountInfoQuery = Mock()
    MapperOfCountInfoQueryResponseVO mapperOfCountInfoQueryResponseVO = Mock()
    ProcessorOfCountInfoQuery processorOfCountInfoQuery = new ProcessorOfCountInfoQuery
            (
                    mapperOfCountInfoQueryRequestType: mapperOfCountInfoQueryRequestType,
                    handlerOfCountInfoQuery: handlerOfCountInfoQuery,
                    mapperOfCountInfoQueryResponseVO: mapperOfCountInfoQueryResponseVO
            )


    def setup() {

    }


    def "test execute"() {
        given:
        (handlerOfCountInfoQuery.handleAsync(_)) >> Mock(WaitFuture)
        when:
        CountInfoQueryResponseVO result = processorOfCountInfoQuery.execute(null)

        then:
        result == null
    }

    def "test tracking"() {
        when:
        Map<String, String> result = processorOfCountInfoQuery.tracking(null, null)

        then:
        result == null
    }
}